{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Container, Flex } from '@/components/layout';\nimport { Button } from '@/components/ui';\nimport { SolarPanel } from '@/components/icons';\nimport {\n  LayoutDashboard,\n  Zap,\n  TrendingUp,\n  Wallet,\n  Users,\n  Shield,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  Bell,\n  AlertCircle,\n  ChevronDown,\n  User,\n  CreditCard,\n  MessageCircle\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nexport const DashboardLayout: React.FC<DashboardLayoutProps> = ({\n  children,\n  activeTab,\n  onTabChange,\n}) => {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [kycBannerDismissed, setKycBannerDismissed] = useState(false);\n  const [userDropdownOpen, setUserDropdownOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Base navigation items\n  const baseNavigationItems = [\n    { id: 'overview', label: 'Overview', icon: LayoutDashboard },\n    { id: 'mining', label: 'Mining Units', icon: Zap },\n    { id: 'earnings', label: 'Earnings', icon: TrendingUp },\n    { id: 'wallet', label: 'Wallet', icon: Wallet },\n    { id: 'referrals', label: 'Network', icon: Users },\n    { id: 'kyc', label: 'KYC Verification', icon: Shield },\n    { id: 'support', label: 'Support', icon: MessageCircle },\n  ];\n\n  // Add admin panel for admin users\n  const navigationItems = user?.role === 'ADMIN'\n    ? [...baseNavigationItems, { id: 'admin', label: 'Admin Panel', icon: Settings }]\n    : baseNavigationItems;\n\n  // Page subtitles\n  const getPageSubtitle = (tabId: string) => {\n    switch (tabId) {\n      case 'overview':\n        return 'Manage your mining operations and earnings';\n      case 'mining':\n        return 'Purchase and manage your mining units';\n      case 'earnings':\n        return 'Track your mining rewards and commissions';\n      case 'wallet':\n        return 'Manage deposits, withdrawals, and balance';\n      case 'referrals':\n        return 'Build and manage your referral network';\n      case 'kyc':\n        return 'Complete your identity verification';\n      case 'support':\n        return 'Get help and manage support tickets';\n      case 'admin':\n        return 'Manage platform operations and users';\n      default:\n        return 'Manage your mining operations and earnings';\n    }\n  };\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  const handleNavigation = (itemId: string) => {\n    if (itemId === 'admin') {\n      // Navigate to admin panel page\n      router.push('/admin');\n    } else {\n      // Handle regular tab changes\n      onTabChange(itemId);\n      setSidebarOpen(false);\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setUserDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Fixed Sidebar */}\n      <aside className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200\n        transform transition-all duration-300 ease-in-out\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n      `}>\n        <div className=\"flex flex-col h-screen\">\n          {/* Logo Header */}\n          <div className=\"flex items-center justify-between h-14 px-5 border-b border-gray-200 bg-white flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center\">\n                <SolarPanel className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-lg font-bold text-gray-900\">HashCoreX</span>\n            </Link>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1.5 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n\n\n          {/* Navigation Menu */}\n          <nav className=\"flex-1 px-3 py-4 space-y-1 min-h-0\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = activeTab === item.id;\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => handleNavigation(item.id)}\n                  className={`\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group\n                    ${isActive\n                      ? 'bg-yellow-500 text-white shadow-md'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                    }\n                  `}\n                >\n                  <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'}`} />\n                  <span className=\"font-medium text-sm\">{item.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n\n          {/* Sidebar Footer */}\n          <div className=\"px-3 py-3 border-t border-gray-200 bg-gray-50 flex-shrink-0\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group\"\n            >\n              <LogOut className=\"h-4 w-4 group-hover:text-red-600\" />\n              <span className=\"font-medium text-sm\">Logout</span>\n            </button>\n          </div>\n        </div>\n      </aside>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col min-w-0 lg:ml-64\">\n        {/* Top Navigation Bar */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12\">\n            <Flex justify=\"between\" align=\"center\" className=\"h-16\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSidebarOpen(true)}\n                  className=\"lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\"\n                >\n                  <Menu className=\"h-6 w-6\" />\n                </button>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-900 capitalize\">\n                    {navigationItems.find(item => item.id === activeTab)?.label || 'Dashboard'}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 hidden sm:block\">\n                    {getPageSubtitle(activeTab)}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3\">\n                {/* Notifications */}\n                <button className=\"relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\">\n                  <Bell className=\"h-5 w-5\" />\n                  <span className=\"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"></span>\n                </button>\n\n                {/* KYC Status - Only show if not approved and on relevant pages */}\n                {user?.kycStatus !== 'APPROVED' && (activeTab === 'kyc' || activeTab === 'wallet') && (\n                  <div className={`\n                    px-3 py-1.5 rounded-lg text-xs font-semibold border\n                    ${user?.kycStatus === 'PENDING'\n                      ? 'bg-solar-50 text-solar-700 border-solar-200'\n                      : 'bg-red-50 text-red-700 border-red-200'\n                    }\n                  `}>\n                    KYC: {user?.kycStatus}\n                  </div>\n                )}\n\n                {/* User Dropdown */}\n                <div className=\"relative\" ref={dropdownRef}>\n                  <button\n                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}\n                    className=\"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\"\n                  >\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-semibold text-sm\">\n                        {user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                    <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {userDropdownOpen && (\n                    <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\">\n                      {/* User Info */}\n                      <div className=\"px-4 py-3 border-b border-gray-100\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-white font-bold\">\n                              {user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-semibold text-gray-900 truncate\">\n                              {user?.firstName && user?.lastName\n                                ? `${user.firstName} ${user.lastName}`\n                                : user?.email.split('@')[0]\n                              }\n                            </p>\n                            <p className=\"text-xs text-gray-600\">\n                              ID: {user?.referralId}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Menu Items */}\n                      <div className=\"py-1\">\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            // Add profile navigation if needed\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                        >\n                          <User className=\"h-4 w-4\" />\n                          <span>Profile Settings</span>\n                        </button>\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            onTabChange('wallet');\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                        >\n                          <CreditCard className=\"h-4 w-4\" />\n                          <span>Billing & Payments</span>\n                        </button>\n                        <div className=\"border-t border-gray-100 my-1\"></div>\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            handleLogout();\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n                        >\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign Out</span>\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </Flex>\n          </div>\n        </header>\n\n        {/* KYC Notification Banner */}\n        {user?.kycStatus !== 'APPROVED' && !kycBannerDismissed && (\n          <div className=\"bg-yellow-50 border-b border-yellow-200\">\n            <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12\">\n              <div className=\"flex items-center justify-between py-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <AlertCircle className=\"h-5 w-5 text-solar-600\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-solar-800\">\n                      {user?.kycStatus === 'PENDING'\n                        ? 'KYC verification in progress'\n                        : 'Complete your KYC verification'\n                      }\n                    </p>\n                    <p className=\"text-xs text-solar-600\">\n                      {user?.kycStatus === 'PENDING'\n                        ? 'Your documents are being reviewed. This usually takes 1-3 business days.'\n                        : 'Verify your identity to enable withdrawals and unlock all features.'\n                      }\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  {user?.kycStatus !== 'PENDING' && (\n                    <Button\n                      size=\"sm\"\n                      onClick={() => onTabChange('kyc')}\n                      className=\"bg-solar-600 hover:bg-solar-700 text-white\"\n                    >\n                      Complete KYC\n                    </Button>\n                  )}\n                  <button\n                    onClick={() => setKycBannerDismissed(true)}\n                    className=\"text-solar-500 hover:text-solar-700 p-1\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <main className=\"flex-1 bg-gray-50 overflow-y-auto\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12 py-6\">\n            <div className=\"max-w-7xl mx-auto\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAkCO,MAAM,kBAAkD,CAAC,EAC9D,QAAQ,EACR,SAAS,EACT,WAAW,EACZ;;IACC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,+NAAA,CAAA,kBAAe;QAAC;QAC3D;YAAE,IAAI;YAAU,OAAO;YAAgB,MAAM,mMAAA,CAAA,MAAG;QAAC;QACjD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;QACtD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAa,OAAO;YAAW,MAAM,uMAAA,CAAA,QAAK;QAAC;QACjD;YAAE,IAAI;YAAO,OAAO;YAAoB,MAAM,yMAAA,CAAA,SAAM;QAAC;QACrD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,2NAAA,CAAA,gBAAa;QAAC;KACxD;IAED,kCAAkC;IAClC,MAAM,kBAAkB,MAAM,SAAS,UACnC;WAAI;QAAqB;YAAE,IAAI;YAAS,OAAO;YAAe,MAAM,6MAAA,CAAA,WAAQ;QAAC;KAAE,GAC/E;IAEJ,iBAAiB;IACjB,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,SAAS;YACtB,+BAA+B;YAC/B,OAAO,IAAI,CAAC;QACd,OAAO;YACL,6BAA6B;YAC7B,YAAY;YACZ,eAAe;QACjB;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAM,WAAW,CAAC;;;QAGjB,EAAE,cAAc,kBAAkB,qCAAqC;MACzE,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4IAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAOjB,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gCAEtC,qBACE,6LAAC;oCAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;oCACvC,WAAW,CAAC;;oBAEV,EAAE,WACE,uCACA,sDACH;kBACH,CAAC;;sDAED,6LAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,2CAA2C;;;;;;sDACjG,6LAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;mCAX5C,KAAK,EAAE;;;;;4BAclB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,OAAM;gCAAS,WAAU;;kDAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,SAAS;;;;;;kEAEjE,6LAAC;wDAAE,WAAU;kEACV,gBAAgB;;;;;;;;;;;;;;;;;;kDAKvB,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;;;;;;;;;;;4CAIjB,MAAM,cAAc,cAAc,CAAC,cAAc,SAAS,cAAc,QAAQ,mBAC/E,6LAAC;gDAAI,WAAW,CAAC;;oBAEf,EAAE,MAAM,cAAc,YAClB,gDACA,wCACH;kBACH,CAAC;;oDAAE;oDACK,MAAM;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;gDAAW,KAAK;;kEAC7B,6LAAC;wDACC,SAAS,IAAM,oBAAoB,CAAC;wDACpC,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EACb,MAAM,WAAW,OAAO,GAAG,iBAAiB,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;0EAGvE,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,eAAe,IAAI;;;;;;;;;;;;oDAI7G,kCACC,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAK,WAAU;0FACb,MAAM,WAAW,OAAO,GAAG,iBAAiB,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;sFAGvE,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;;;;;;8FAG/B,6LAAC;oFAAE,WAAU;;wFAAwB;wFAC9B,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0EAOnB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS;4EACP,oBAAoB;wEACpB,mCAAmC;wEACrC;wEACA,WAAU;;0FAEV,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEACC,SAAS;4EACP,oBAAoB;4EACpB,YAAY;wEACd;wEACA,WAAU;;0FAEV,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEACC,SAAS;4EACP,oBAAoB;4EACpB;wEACF;wEACA,WAAU;;0FAEV,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAYvB,MAAM,cAAc,cAAc,CAAC,oCAClC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEACV,MAAM,cAAc,YACjB,iCACA;;;;;;kEAGN,6LAAC;wDAAE,WAAU;kEACV,MAAM,cAAc,YACjB,6EACA;;;;;;;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,cAAc,2BACnB,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;0DAIH,6LAAC;gDACC,SAAS,IAAM,sBAAsB;gDACrC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GA7Ua;;QAKc,2HAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KANb", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/DashboardOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, Button } from '@/components/ui';\nimport { Grid, GridItem } from '@/components/layout';\nimport { MiningRig, Cryptocurrency, SolarPanel } from '@/components/icons';\nimport { TrendingUp, Wallet, Users, Zap, Clock, Award } from 'lucide-react';\nimport { formatCurrency, formatTHS, getTimeUntilNextPayout } from '@/lib/utils';\n\ninterface DashboardStats {\n  totalTHS: number;\n  estimatedEarnings: {\n    next7Days: number;\n    next30Days: number;\n    next365Days: number;\n  };\n  walletBalance: number;\n  pendingEarnings: number;\n  activeUnits: number;\n  totalEarnings: number;\n  directReferrals: number;\n  binaryPoints: {\n    leftPoints: number;\n    rightPoints: number;\n  };\n}\n\ninterface DashboardOverviewProps {\n  onTabChange?: (tab: string) => void;\n}\n\nexport const DashboardOverview: React.FC<DashboardOverviewProps> = ({ onTabChange }) => {\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());\n\n  useEffect(() => {\n    fetchDashboardStats();\n    \n    // Update countdown every second\n    const interval = setInterval(() => {\n      setTimeUntilPayout(getTimeUntilNextPayout());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchDashboardStats = async () => {\n    try {\n      // Fetch data from multiple endpoints\n      const [walletRes, earningsRes, miningRes, referralRes] = await Promise.all([\n        fetch('/api/wallet/balance', { credentials: 'include' }),\n        fetch('/api/earnings', { credentials: 'include' }),\n        fetch('/api/mining-units', { credentials: 'include' }),\n        fetch('/api/referrals/tree?depth=1', { credentials: 'include' }),\n      ]);\n\n      const [walletData, earningsData, miningData, referralData] = await Promise.all([\n        walletRes.json(),\n        earningsRes.json(),\n        miningRes.json(),\n        referralRes.json(),\n      ]);\n\n      if (walletData.success && earningsData.success && miningData.success && referralData.success) {\n        const totalTHS = miningData.data.reduce((sum: number, unit: any) => sum + unit.thsAmount, 0);\n        \n        setStats({\n          totalTHS,\n          estimatedEarnings: earningsData.data.estimatedEarnings,\n          walletBalance: walletData.data.balance,\n          pendingEarnings: walletData.data.pendingEarnings,\n          activeUnits: miningData.data.length,\n          totalEarnings: earningsData.data.totalEarnings,\n          directReferrals: referralData.data.statistics.totalDirectReferrals,\n          binaryPoints: referralData.data.statistics.binaryPoints,\n        });\n      }\n    } catch (error) {\n      console.error('Failed to fetch dashboard stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 3 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!stats) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load dashboard data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Section */}\n      <div className=\"bg-green-600 rounded-2xl p-8 text-white shadow-lg\">\n        <h1 className=\"text-3xl font-bold mb-3\">Welcome to HashCoreX</h1>\n        <p className=\"text-green-100 text-lg leading-relaxed\">\n          Your sustainable mining dashboard. Track your earnings, manage your mining units, and grow your referral network.\n        </p>\n      </div>\n\n      {/* Key Metrics */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Key Metrics</h2>\n        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Mining Power</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatTHS(stats.totalTHS)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Zap className=\"h-7 w-7 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Wallet Balance</p>\n                  <p className=\"text-3xl font-bold text-eco-600\">\n                    {formatCurrency(stats.walletBalance)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <Wallet className=\"h-7 w-7 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(stats.totalEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Direct Referrals</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {stats.directReferrals}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-purple-100 rounded-xl flex items-center justify-center\">\n                  <Users className=\"h-7 w-7 text-purple-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Earnings and Payout Section */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Earnings Overview</h2>\n        <Grid cols={{ default: 1, lg: 2 }} gap={8}>\n          {/* Estimated Earnings */}\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardHeader className=\"pb-4\">\n              <CardTitle className=\"flex items-center space-x-3 text-lg\">\n                <div className=\"h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center\">\n                  <TrendingUp className=\"h-5 w-5 text-eco-600\" />\n                </div>\n                <span>Estimated Earnings</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"pt-0\">\n              <div className=\"space-y-5\">\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 7 Days</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next7Days)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 30 Days</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next30Days)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 365 Days</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next365Days)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 2 Years</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next2Years)}\n                  </span>\n                </div>\n              </div>\n              <div className=\"mt-6 p-4 bg-eco-50 rounded-xl\">\n                <p className=\"text-sm text-eco-700 font-medium\">\n                  * Based on current mining units and average ROI\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Next Payout */}\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardHeader className=\"pb-4\">\n              <CardTitle className=\"flex items-center space-x-3 text-lg\">\n                <div className=\"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center\">\n                  <Clock className=\"h-5 w-5 text-solar-600\" />\n                </div>\n                <span>Next Payout</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"pt-0\">\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-600 mb-6 font-medium\">\n                  Weekly payout every Saturday at 15:00 UTC\n                </p>\n                <div className=\"grid grid-cols-4 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.days}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Days</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.hours}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Hours</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.minutes}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Min</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.seconds}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Sec</div>\n                  </div>\n                </div>\n                {stats.pendingEarnings > 0 && (\n                  <div className=\"mt-6 p-4 bg-solar-50 rounded-xl\">\n                    <p className=\"text-sm text-solar-700 font-semibold\">\n                      <strong className=\"text-lg\">{formatCurrency(stats.pendingEarnings)}</strong> pending\n                    </p>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Quick Actions */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Quick Actions</h2>\n        <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n          <CardContent className=\"p-8\">\n            <Grid cols={{ default: 1, sm: 2, lg: 3 }} gap={6}>\n              <Button\n                className=\"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-0\"\n                onClick={() => onTabChange?.('mining')}\n              >\n                <MiningRig className=\"h-7 w-7\" />\n                <span>Buy Mining Power</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600 hover:bg-green-50 hover:border-green-600 hover:text-green-700\"\n                onClick={() => onTabChange?.('wallet')}\n              >\n                <Cryptocurrency className=\"h-7 w-7\" />\n                <span>Withdraw USDT</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600 hover:bg-green-50 hover:border-green-600 hover:text-green-700\"\n                onClick={() => onTabChange?.('referrals')}\n              >\n                <Users className=\"h-7 w-7\" />\n                <span>Build Network</span>\n              </Button>\n            </Grid>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Binary Points Summary */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Binary Network Summary</h2>\n        <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n          <CardHeader className=\"pb-4\">\n            <CardTitle className=\"flex items-center space-x-3 text-lg\">\n              <div className=\"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center\">\n                <Award className=\"h-5 w-5 text-solar-600\" />\n              </div>\n              <span>Network Performance</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"pt-0\">\n            <Grid cols={{ default: 1, sm: 3 }} gap={8}>\n              <div className=\"text-center\">\n                <div className=\"bg-solar-50 rounded-xl p-6 mb-3\">\n                  <div className=\"text-3xl font-bold text-solar-600\">\n                    {stats.binaryPoints.leftPoints}\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600 font-medium\">Left Points</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"bg-solar-50 rounded-xl p-6 mb-3\">\n                  <div className=\"text-3xl font-bold text-solar-600\">\n                    {stats.binaryPoints.rightPoints}\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600 font-medium\">Right Points</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"bg-eco-50 rounded-xl p-6 mb-3\">\n                  <div className=\"text-3xl font-bold text-eco-600\">\n                    {Math.min(stats.binaryPoints.leftPoints, stats.binaryPoints.rightPoints)}\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600 font-medium\">Potential Match</div>\n              </div>\n            </Grid>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AA+BO,MAAM,oBAAsD,CAAC,EAAE,WAAW,EAAE;;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;IAE5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;YAEA,gCAAgC;YAChC,MAAM,WAAW;wDAAY;oBAC3B,mBAAmB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAC1C;uDAAG;YAEH;+CAAO,IAAM,cAAc;;QAC7B;sCAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,qCAAqC;YACrC,MAAM,CAAC,WAAW,aAAa,WAAW,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzE,MAAM,uBAAuB;oBAAE,aAAa;gBAAU;gBACtD,MAAM,iBAAiB;oBAAE,aAAa;gBAAU;gBAChD,MAAM,qBAAqB;oBAAE,aAAa;gBAAU;gBACpD,MAAM,+BAA+B;oBAAE,aAAa;gBAAU;aAC/D;YAED,MAAM,CAAC,YAAY,cAAc,YAAY,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7E,UAAU,IAAI;gBACd,YAAY,IAAI;gBAChB,UAAU,IAAI;gBACd,YAAY,IAAI;aACjB;YAED,IAAI,WAAW,OAAO,IAAI,aAAa,OAAO,IAAI,WAAW,OAAO,IAAI,aAAa,OAAO,EAAE;gBAC5F,MAAM,WAAW,WAAW,IAAI,CAAC,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,KAAK,SAAS,EAAE;gBAE1F,SAAS;oBACP;oBACA,mBAAmB,aAAa,IAAI,CAAC,iBAAiB;oBACtD,eAAe,WAAW,IAAI,CAAC,OAAO;oBACtC,iBAAiB,WAAW,IAAI,CAAC,eAAe;oBAChD,aAAa,WAAW,IAAI,CAAC,MAAM;oBACnC,eAAe,aAAa,IAAI,CAAC,aAAa;oBAC9C,iBAAiB,aAAa,IAAI,CAAC,UAAU,CAAC,oBAAoB;oBAClE,cAAc,aAAa,IAAI,CAAC,UAAU,CAAC,YAAY;gBACzD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;0BAMxD,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ;;;;;;;;;;;;0DAG7B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,aAAa;;;;;;;;;;;;0DAGvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,aAAa;;;;;;;;;;;;0DAGvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,MAAM,eAAe;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAEtC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,SAAS;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,UAAU;;;;;;;;;;;;kEAGtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,WAAW;;;;;;;;;;;;kEAGvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,UAAU;;;;;;;;;;;;;;;;;;0DAIxD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;0CAQtD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,IAAI;;;;;;;;;;;8EAGzB,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAErD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,KAAK;;;;;;;;;;;8EAG1B,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAErD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,OAAO;;;;;;;;;;;8EAG5B,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAErD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,OAAO;;;;;;;;;;;8EAG5B,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;gDAGtD,MAAM,eAAe,GAAG,mBACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAW,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;4DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5F,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC,uIAAA,CAAA,OAAI;gCAAC,MAAM;oCAAE,SAAS;oCAAG,IAAI;oCAAG,IAAI;gCAAE;gCAAG,KAAK;;kDAC7C,6LAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,6LAAC,2IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,6LAAC,gJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,uIAAA,CAAA,OAAI;oCAAC,MAAM;wCAAE,SAAS;wCAAG,IAAI;oCAAE;oCAAG,KAAK;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,MAAM,YAAY,CAAC,UAAU;;;;;;;;;;;8DAGlC,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;8DAGnC,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,KAAK,GAAG,CAAC,MAAM,YAAY,CAAC,UAAU,EAAE,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;8DAG3E,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GAzVa;KAAA", "debugId": null}}, {"offset": {"line": 1994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/PurchaseMiningUnit.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Card, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { MiningRig, Cryptocurrency } from '@/components/icons';\nimport { Calculator, DollarSign, Zap } from 'lucide-react';\nimport { formatCurrency, formatNumber } from '@/lib/utils';\n\ninterface PurchaseMiningUnitProps {\n  onPurchaseComplete?: () => void;\n}\n\nexport const PurchaseMiningUnit: React.FC<PurchaseMiningUnitProps> = ({\n  onPurchaseComplete,\n}) => {\n  const [formData, setFormData] = useState({\n    thsAmount: '',\n    investmentAmount: '',\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [thsPrice, setThsPrice] = useState(50); // Default price\n  const [earningsRanges, setEarningsRanges] = useState([\n    { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n    { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n    { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n  ]);\n  const [minPurchaseAmount, setMinPurchaseAmount] = useState(100);\n\n  // Fetch current TH/s price and ROI range\n  useEffect(() => {\n    fetchPricing();\n  }, []);\n\n  const fetchPricing = async () => {\n    try {\n      const response = await fetch('/api/admin/settings/pricing');\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setThsPrice(data.data.thsPrice);\n          setEarningsRanges(data.data.earningsRanges);\n          setMinPurchaseAmount(data.data.minPurchaseAmount);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch pricing:', error);\n    }\n  };\n\n  const handleThsChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const ths = parseFloat(e.target.value) || 0;\n    const investment = ths * thsPrice;\n    \n    setFormData({\n      thsAmount: e.target.value,\n      investmentAmount: investment > 0 ? investment.toFixed(2) : '',\n    });\n  };\n\n  const handleInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const investment = parseFloat(e.target.value) || 0;\n    const ths = investment / thsPrice;\n\n    setFormData({\n      thsAmount: ths > 0 ? ths.toFixed(4) : '',\n      investmentAmount: e.target.value,\n    });\n  };\n\n  // Get the appropriate earnings range for a given TH/s amount\n  const getEarningsRangeForTHS = (thsAmount: number) => {\n    const range = earningsRanges.find(range =>\n      thsAmount >= range.minTHS && thsAmount <= range.maxTHS\n    );\n    return range || earningsRanges[earningsRanges.length - 1]; // Fallback to highest range\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const thsAmount = parseFloat(formData.thsAmount);\n      const investmentAmount = parseFloat(formData.investmentAmount);\n\n      if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {\n        throw new Error('Please enter valid amounts');\n      }\n\n      if (investmentAmount < minPurchaseAmount) {\n        throw new Error(`Minimum purchase amount is $${minPurchaseAmount}`);\n      }\n\n      const response = await fetch('/api/mining-units', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          thsAmount,\n          investmentAmount,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Purchase failed');\n      }\n\n      // Reset form\n      setFormData({\n        thsAmount: '',\n        investmentAmount: '',\n      });\n\n      // Notify parent component\n      if (onPurchaseComplete) {\n        onPurchaseComplete();\n      }\n\n    } catch (err: any) {\n      setError(err.message || 'Purchase failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateEstimatedEarnings = () => {\n    const investment = parseFloat(formData.investmentAmount) || 0;\n    const thsAmount = parseFloat(formData.thsAmount) || 0;\n    if (investment <= 0 || thsAmount <= 0) return { daily: 0, weekly: 0, monthly: 0, range: null };\n\n    const range = getEarningsRangeForTHS(thsAmount);\n    const avgROI = (range.dailyReturnMin + range.dailyReturnMax) / 2;\n    const daily = (investment * avgROI) / 100;\n\n    return {\n      daily,\n      weekly: daily * 7,\n      monthly: daily * 30,\n      range,\n    };\n  };\n\n  const estimatedEarnings = calculateEstimatedEarnings();\n\n  return (\n    <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n      <CardHeader className=\"pb-4\">\n        <CardTitle className=\"flex items-center space-x-3 text-xl\">\n          <div className=\"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center\">\n            <MiningRig className=\"h-6 w-6 text-solar-600\" />\n          </div>\n          <span>Purchase Mining Power</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-xl text-sm font-medium\">\n              {error}\n            </div>\n          )}\n\n          {/* Current Pricing Info */}\n          <div className=\"bg-gradient-to-r from-solar-50 to-eco-50 rounded-xl p-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4\">\n              <div className=\"text-center sm:text-left\">\n                <span className=\"text-sm font-medium text-gray-600 block mb-1\">Current TH/s Price</span>\n                <span className=\"text-2xl font-bold text-solar-600\">\n                  {formatCurrency(thsPrice)} / TH/s\n                </span>\n              </div>\n              <div className=\"text-center sm:text-right\">\n                <span className=\"text-sm font-medium text-gray-600 block mb-1\">\n                  {estimatedEarnings.range ?\n                    `Daily ROI Range (${estimatedEarnings.range.minTHS}-${estimatedEarnings.range.maxTHS} TH/s)` :\n                    'Daily ROI Range'\n                  }\n                </span>\n                <span className=\"text-xl font-bold text-eco-600\">\n                  {estimatedEarnings.range ?\n                    `${estimatedEarnings.range.dailyReturnMin}% - ${estimatedEarnings.range.dailyReturnMax}%` :\n                    '0.3% - 0.7%'\n                  }\n                </span>\n              </div>\n            </div>\n\n            {/* Dynamic Earnings Ranges */}\n            <div className=\"border-t border-gray-200 pt-4\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-3\">TH/s Based Earnings Tiers</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n                {earningsRanges.map((range, index) => (\n                  <div key={index} className=\"bg-white rounded-lg p-3 border border-gray-200\">\n                    <div className=\"text-xs font-medium text-gray-600 mb-1\">\n                      {range.minTHS} - {range.maxTHS === 999999 ? '∞' : range.maxTHS} TH/s\n                    </div>\n                    <div className=\"text-sm font-bold text-eco-600\">\n                      {range.dailyReturnMin}% - {range.dailyReturnMax}%\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      Monthly: {range.monthlyReturnMin}% - {range.monthlyReturnMax}%\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Input Fields */}\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <div>\n                <Input\n                  label=\"TH/s Amount\"\n                  type=\"number\"\n                  step=\"0.0001\"\n                  min=\"0\"\n                  value={formData.thsAmount}\n                  onChange={handleThsChange}\n                  placeholder=\"Enter TH/s amount\"\n                  leftIcon={<Zap className=\"h-4 w-4\" />}\n                  className=\"h-12\"\n                />\n              </div>\n\n              <div>\n                <Input\n                  label=\"Investment Amount (USD)\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  min={minPurchaseAmount}\n                  value={formData.investmentAmount}\n                  onChange={handleInvestmentChange}\n                  placeholder=\"Enter investment amount\"\n                  leftIcon={<DollarSign className=\"h-4 w-4\" />}\n                  className=\"h-12\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Estimated Earnings */}\n          {estimatedEarnings.daily > 0 && (\n            <div className=\"bg-gradient-to-r from-eco-50 to-green-50 rounded-xl p-6\">\n              <h4 className=\"text-base font-semibold text-gray-800 mb-4 flex items-center\">\n                <div className=\"h-8 w-8 bg-eco-100 rounded-lg flex items-center justify-center mr-3\">\n                  <Calculator className=\"h-4 w-4 text-eco-600\" />\n                </div>\n                Estimated Earnings {estimatedEarnings.range ?\n                  `(Average ROI: ${formatNumber((estimatedEarnings.range.dailyReturnMin + estimatedEarnings.range.dailyReturnMax) / 2, 1)}%)` :\n                  ''\n                }\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"bg-white rounded-xl p-4 shadow-sm\">\n                    <div className=\"text-2xl font-bold text-eco-600 mb-1\">\n                      {formatCurrency(estimatedEarnings.daily)}\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Daily</div>\n                  </div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"bg-white rounded-xl p-4 shadow-sm\">\n                    <div className=\"text-2xl font-bold text-eco-600 mb-1\">\n                      {formatCurrency(estimatedEarnings.weekly)}\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Weekly</div>\n                  </div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"bg-white rounded-xl p-4 shadow-sm\">\n                    <div className=\"text-2xl font-bold text-eco-600 mb-1\">\n                      {formatCurrency(estimatedEarnings.monthly)}\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Monthly</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Important Notes */}\n          <div className=\"bg-gray-50 rounded-xl p-6\">\n            <h4 className=\"text-base font-semibold text-gray-800 mb-4\">Important Notes:</h4>\n            <ul className=\"text-sm text-gray-600 space-y-2\">\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Minimum purchase: ${minPurchaseAmount}</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Mining units are active for 24 months</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Units expire when 5x investment is earned</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Weekly payouts every Saturday at 15:00 UTC</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>ROI varies daily based on mining performance</span>\n              </li>\n            </ul>\n          </div>\n\n          <Button\n            type=\"submit\"\n            size=\"lg\"\n            className=\"w-full h-14 text-lg font-semibold rounded-xl\"\n            loading={loading}\n            disabled={!formData.thsAmount || !formData.investmentAmount || parseFloat(formData.investmentAmount) < minPurchaseAmount}\n          >\n            <Cryptocurrency className=\"h-6 w-6 mr-3\" />\n            Purchase Mining Unit\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAYO,MAAM,qBAAwD,CAAC,EACpE,kBAAkB,EACnB;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,kBAAkB;IACpB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gBAAgB;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD;YAAE,QAAQ;YAAG,QAAQ;YAAI,gBAAgB;YAAK,gBAAgB;YAAK,kBAAkB;YAAM,kBAAkB;QAAK;QAClH;YAAE,QAAQ;YAAI,QAAQ;YAAI,gBAAgB;YAAK,gBAAgB;YAAK,kBAAkB;YAAM,kBAAkB;QAAK;QACnH;YAAE,QAAQ;YAAI,QAAQ;YAAQ,gBAAgB;YAAK,gBAAgB;YAAK,kBAAkB;YAAM,kBAAkB;QAAK;KACxH;IACD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI,CAAC,QAAQ;oBAC9B,kBAAkB,KAAK,IAAI,CAAC,cAAc;oBAC1C,qBAAqB,KAAK,IAAI,CAAC,iBAAiB;gBAClD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;QAC1C,MAAM,aAAa,MAAM;QAEzB,YAAY;YACV,WAAW,EAAE,MAAM,CAAC,KAAK;YACzB,kBAAkB,aAAa,IAAI,WAAW,OAAO,CAAC,KAAK;QAC7D;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;QACjD,MAAM,MAAM,aAAa;QAEzB,YAAY;YACV,WAAW,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK;YACtC,kBAAkB,EAAE,MAAM,CAAC,KAAK;QAClC;IACF;IAEA,6DAA6D;IAC7D,MAAM,yBAAyB,CAAC;QAC9B,MAAM,QAAQ,eAAe,IAAI,CAAC,CAAA,QAChC,aAAa,MAAM,MAAM,IAAI,aAAa,MAAM,MAAM;QAExD,OAAO,SAAS,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,EAAE,4BAA4B;IACzF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,YAAY,WAAW,SAAS,SAAS;YAC/C,MAAM,mBAAmB,WAAW,SAAS,gBAAgB;YAE7D,IAAI,CAAC,aAAa,CAAC,oBAAoB,aAAa,KAAK,oBAAoB,GAAG;gBAC9E,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,mBAAmB,mBAAmB;gBACxC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,mBAAmB;YACpE;YAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,kBAAkB;YACpB;YAEA,0BAA0B;YAC1B,IAAI,oBAAoB;gBACtB;YACF;QAEF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B;QACjC,MAAM,aAAa,WAAW,SAAS,gBAAgB,KAAK;QAC5D,MAAM,YAAY,WAAW,SAAS,SAAS,KAAK;QACpD,IAAI,cAAc,KAAK,aAAa,GAAG,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,SAAS;YAAG,OAAO;QAAK;QAE7F,MAAM,QAAQ,uBAAuB;QACrC,MAAM,SAAS,CAAC,MAAM,cAAc,GAAG,MAAM,cAAc,IAAI;QAC/D,MAAM,QAAQ,AAAC,aAAa,SAAU;QAEtC,OAAO;YACL;YACA,QAAQ,QAAQ;YAChB,SAAS,QAAQ;YACjB;QACF;IACF;IAEA,MAAM,oBAAoB;IAE1B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA+C;;;;;;8DAC/D,6LAAC;oDAAK,WAAU;;wDACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;wDAAU;;;;;;;;;;;;;sDAG9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,kBAAkB,KAAK,GACtB,CAAC,iBAAiB,EAAE,kBAAkB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,kBAAkB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAC5F;;;;;;8DAGJ,6LAAC;oDAAK,WAAU;8DACb,kBAAkB,KAAK,GACtB,GAAG,kBAAkB,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,GACzF;;;;;;;;;;;;;;;;;;8CAOR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM;gEAAC;gEAAI,MAAM,MAAM,KAAK,SAAS,MAAM,MAAM,MAAM;gEAAC;;;;;;;sEAEjE,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,cAAc;gEAAC;gEAAK,MAAM,cAAc;gEAAC;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;gEAAwB;gEAC3B,MAAM,gBAAgB;gEAAC;gEAAK,MAAM,gBAAgB;gEAAC;;;;;;;;mDARvD;;;;;;;;;;;;;;;;;;;;;;sCAiBlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,aAAY;4CACZ,wBAAU,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACzB,WAAU;;;;;;;;;;;kDAId,6LAAC;kDACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,KAAK;4CACL,OAAO,SAAS,gBAAgB;4CAChC,UAAU;4CACV,aAAY;4CACZ,wBAAU,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAChC,WAAU;;;;;;;;;;;;;;;;;;;;;;wBAOjB,kBAAkB,KAAK,GAAG,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;wCAClB;wCACc,kBAAkB,KAAK,GACzC,CAAC,cAAc,EAAE,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,CAAC,kBAAkB,KAAK,CAAC,cAAc,GAAG,kBAAkB,KAAK,CAAC,cAAc,IAAI,GAAG,GAAG,EAAE,CAAC,GAC3H;;;;;;;8CAGJ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,KAAK;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,MAAM;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,OAAO;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;;wDAAK;wDAAoB;;;;;;;;;;;;;sDAE5B,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAKZ,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,WAAU;4BACV,SAAS;4BACT,UAAU,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,gBAAgB,IAAI,WAAW,SAAS,gBAAgB,IAAI;;8CAEvG,6LAAC,gJAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA7Ta;KAAA", "debugId": null}}, {"offset": {"line": 2767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/EarningsTracker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { Grid, GridItem } from '@/components/layout';\nimport { TrendingUp, DollarSign, Clock, Calendar } from 'lucide-react';\nimport { formatCurrency, formatDateTime, getTimeUntilNextPayout } from '@/lib/utils';\n\ninterface EarningsData {\n  totalEarnings: number;\n  pendingEarnings: number;\n  miningEarnings: number;\n  referralEarnings: number;\n  estimatedEarnings: {\n    next7Days: number;\n    next30Days: number;\n    next365Days: number;\n  };\n  recentEarnings: Array<{\n    id: string;\n    type: string;\n    amount: number;\n    description: string;\n    createdAt: string;\n  }>;\n  earningsBreakdown: {\n    mining: number;\n    directReferral: number;\n    binaryBonus: number;\n  };\n}\n\nexport const EarningsTracker: React.FC = () => {\n  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());\n\n  useEffect(() => {\n    fetchEarningsData();\n    \n    // Update countdown every second\n    const interval = setInterval(() => {\n      setTimeUntilPayout(getTimeUntilNextPayout());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchEarningsData = async () => {\n    try {\n      const response = await fetch('/api/earnings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setEarningsData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch earnings data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!earningsData) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load earnings data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const getEarningsTypeColor = (type: string) => {\n    switch (type) {\n      case 'MINING_EARNINGS':\n        return 'text-solar-600';\n      case 'DIRECT_REFERRAL':\n        return 'text-eco-600';\n      case 'BINARY_BONUS':\n        return 'text-blue-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getEarningsTypeLabel = (type: string) => {\n    switch (type) {\n      case 'MINING_EARNINGS':\n        return 'Mining';\n      case 'DIRECT_REFERRAL':\n        return 'Direct Referral';\n      case 'BINARY_BONUS':\n        return 'Binary Bonus';\n      default:\n        return type;\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Earnings Overview */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Earnings Overview</h2>\n        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(earningsData.totalEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <DollarSign className=\"h-7 w-7 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Pending Earnings</p>\n                  <p className=\"text-3xl font-bold text-solar-600\">\n                    {formatCurrency(earningsData.pendingEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Clock className=\"h-7 w-7 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Mining Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(earningsData.miningEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-gray-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-gray-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Referral Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(earningsData.referralEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      <Grid cols={{ default: 1, lg: 2 }} gap={6}>\n        {/* Next Payout Countdown */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Calendar className=\"h-5 w-5 text-solar-500\" />\n              <span>Next Payout</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center\">\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Weekly payout every Saturday at 15:00 UTC\n              </p>\n              <div className=\"grid grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.days}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Days</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.hours}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Hours</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.minutes}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Minutes</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.seconds}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Seconds</div>\n                </div>\n              </div>\n              {earningsData.pendingEarnings > 0 && (\n                <div className=\"mt-4 p-3 bg-solar-50 rounded-lg\">\n                  <p className=\"text-sm text-solar-700\">\n                    <strong>{formatCurrency(earningsData.pendingEarnings)}</strong> will be transferred to your wallet\n                  </p>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Estimated Earnings */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-5 w-5 text-eco-500\" />\n              <span>Estimated Earnings</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 7 Days</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next7Days)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 30 Days</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next30Days)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 365 Days</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next365Days)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 2 Years</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next2Years)}\n                </span>\n              </div>\n            </div>\n            <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n              <p className=\"text-xs text-gray-600\">\n                * Estimates based on current mining units and average ROI\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Recent Earnings */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Earnings</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {earningsData.recentEarnings.length > 0 ? (\n            <div className=\"space-y-3\">\n              {earningsData.recentEarnings.slice(0, 10).map((earning) => (\n                <div key={earning.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`text-sm font-medium ${getEarningsTypeColor(earning.type)}`}>\n                        {getEarningsTypeLabel(earning.type)}\n                      </span>\n                      <span className=\"text-xs text-gray-500\">\n                        {formatDateTime(earning.createdAt)}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-1\">{earning.description}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <span className=\"font-semibold text-eco-600\">\n                      +{formatCurrency(earning.amount)}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No earnings yet</p>\n              <p className=\"text-sm text-gray-400 mt-1\">\n                Purchase mining units to start earning\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAgCO,MAAM,kBAA4B;;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;IAE5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YAEA,gCAAgC;YAChC,MAAM,WAAW;sDAAY;oBAC3B,mBAAmB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAC1C;qDAAG;YAEH;6CAAO,IAAM,cAAc;;QAC7B;oCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,aAAa;;;;;;;;;;;;0DAG9C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,eAAe;;;;;;;;;;;;0DAGhD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,cAAc;;;;;;;;;;;;0DAG/C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,gBAAgB;;;;;;;;;;;;0DAGjD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC,uIAAA,CAAA,OAAI;gBAAC,MAAM;oBAAE,SAAS;oBAAG,IAAI;gBAAE;gBAAG,KAAK;;kCAEtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,IAAI;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,KAAK;;;;;;sEAExB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,OAAO;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,OAAO;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;wCAG1C,aAAa,eAAe,GAAG,mBAC9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAQ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,eAAe;;;;;;oDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3E,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,SAAS;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,UAAU;;;;;;;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,WAAW;;;;;;;;;;;;0DAG9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,UAAU;;;;;;;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACT,aAAa,cAAc,CAAC,MAAM,GAAG,kBACpC,6LAAC;4BAAI,WAAU;sCACZ,aAAa,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,wBAC7C,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,qBAAqB,QAAQ,IAAI,GAAG;sEACzE,qBAAqB,QAAQ,IAAI;;;;;;sEAEpC,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;8DAGrC,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAA6B;oDACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;;mCAd3B,QAAQ,EAAE;;;;;;;;;iDAqBxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GAjSa;KAAA", "debugId": null}}, {"offset": {"line": 3698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/WalletDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Card, CardHeader, CardTitle, CardContent, Modal } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { Wallet, ArrowUpRight, ArrowDownLeft, Clock, CheckCircle, XCircle, Copy, DollarSign } from 'lucide-react';\nimport { formatCurrency, formatDateTime, copyToClipboard } from '@/lib/utils';\nimport { DepositPage } from '@/components/wallet/DepositPage';\n\ninterface WalletData {\n  balance: number;\n  pendingEarnings: number;\n  recentTransactions: Array<{\n    id: string;\n    type: string;\n    amount: number;\n    description: string;\n    status: string;\n    createdAt: string;\n  }>;\n}\n\ninterface WithdrawalHistory {\n  id: string;\n  amount: number;\n  usdtAddress: string;\n  status: string;\n  txid?: string;\n  createdAt: string;\n  processedAt?: string;\n  rejectionReason?: string;\n}\n\ninterface WithdrawalSettings {\n  minWithdrawalAmount: number;\n  fixedFee: number;\n  percentageFee: number;\n  processingDays: number;\n}\n\nexport const WalletDashboard: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [walletData, setWalletData] = useState<WalletData | null>(null);\n  const [withdrawalHistory, setWithdrawalHistory] = useState<WithdrawalHistory[]>([]);\n  const [withdrawalSettings, setWithdrawalSettings] = useState<WithdrawalSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [showWithdrawModal, setShowWithdrawModal] = useState(false);\n  const [withdrawalForm, setWithdrawalForm] = useState({\n    amount: '',\n    usdtAddress: '',\n  });\n  const [withdrawalLoading, setWithdrawalLoading] = useState(false);\n  const [withdrawalError, setWithdrawalError] = useState('');\n\n  useEffect(() => {\n    fetchWalletData();\n    fetchWithdrawalHistory();\n    fetchWithdrawalSettings();\n  }, []);\n\n  const fetchWalletData = async () => {\n    try {\n      const response = await fetch('/api/wallet/balance', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWalletData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch wallet data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchWithdrawalHistory = async () => {\n    try {\n      const response = await fetch('/api/wallet/withdraw', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWithdrawalHistory(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch withdrawal history:', error);\n    }\n  };\n\n  const fetchWithdrawalSettings = async () => {\n    try {\n      const response = await fetch('/api/wallet/withdrawal-settings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWithdrawalSettings(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch withdrawal settings:', error);\n    }\n  };\n\n\n\n  const handleWithdrawal = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setWithdrawalError('');\n    setWithdrawalLoading(true);\n\n    try {\n      const amount = parseFloat(withdrawalForm.amount);\n      \n      if (!amount || amount <= 0) {\n        throw new Error('Please enter a valid amount');\n      }\n\n      if (!withdrawalForm.usdtAddress) {\n        throw new Error('Please enter a USDT address');\n      }\n\n      const response = await fetch('/api/wallet/withdraw', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          amount,\n          usdtAddress: withdrawalForm.usdtAddress,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Withdrawal failed');\n      }\n\n      // Reset form and close modal\n      setWithdrawalForm({ amount: '', usdtAddress: '' });\n      setShowWithdrawModal(false);\n      \n      // Refresh data\n      fetchWalletData();\n      fetchWithdrawalHistory();\n\n    } catch (err: any) {\n      setWithdrawalError(err.message || 'Withdrawal failed');\n    } finally {\n      setWithdrawalLoading(false);\n    }\n  };\n\n  const getTransactionIcon = (type: string) => {\n    switch (type) {\n      case 'WITHDRAWAL':\n        return <ArrowUpRight className=\"h-4 w-4 text-red-500\" />;\n      case 'DEPOSIT':\n      case 'MINING_EARNINGS':\n      case 'DIRECT_REFERRAL':\n      case 'BINARY_BONUS':\n        return <ArrowDownLeft className=\"h-4 w-4 text-eco-500\" />;\n      default:\n        return <Wallet className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getTransactionColor = (type: string) => {\n    switch (type) {\n      case 'WITHDRAWAL':\n      case 'PURCHASE':\n        return 'text-red-600';\n      case 'DEPOSIT':\n      case 'MINING_EARNINGS':\n      case 'DIRECT_REFERRAL':\n      case 'BINARY_BONUS':\n        return 'text-eco-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'APPROVED':\n        return <CheckCircle className=\"h-4 w-4 text-eco-500\" />;\n      case 'PENDING':\n        return <Clock className=\"h-4 w-4 text-solar-500\" />;\n      case 'FAILED':\n      case 'REJECTED':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />;\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const handleCopyAddress = async (address: string) => {\n    try {\n      await copyToClipboard(address);\n      // You could add a toast notification here\n    } catch (error) {\n      console.error('Failed to copy address:', error);\n    }\n  };\n\n  const calculateWithdrawalFees = (amount: number) => {\n    if (!withdrawalSettings || !amount) {\n      return { fixedFee: 0, percentageFee: 0, totalFees: 0, totalDeduction: 0, netAmount: 0 };\n    }\n\n    const fixedFee = withdrawalSettings.fixedFee;\n    const percentageFee = (amount * withdrawalSettings.percentageFee) / 100;\n    const totalFees = fixedFee + percentageFee;\n    const totalDeduction = amount + totalFees;\n    const netAmount = amount;\n\n    return { fixedFee, percentageFee, totalFees, totalDeduction, netAmount };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!walletData) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load wallet data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: Wallet },\n    { id: 'deposit', label: 'Deposit', icon: ArrowDownLeft },\n    { id: 'withdraw', label: 'Withdraw', icon: ArrowUpRight },\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'deposit':\n        return <DepositPage />;\n      case 'withdraw':\n        return renderWithdrawContent();\n      default:\n        return renderOverviewContent();\n    }\n  };\n\n  const renderOverviewContent = () => (\n    <div className=\"space-y-8\">\n      {/* Wallet Overview */}\n      <div>\n        <Grid cols={{ default: 1, lg: 2 }} gap={8}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Available Balance</p>\n                  <p className=\"text-4xl font-bold text-dark-900\">\n                    {formatCurrency(walletData.balance)}\n                  </p>\n                </div>\n                <div className=\"h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <Wallet className=\"h-8 w-8 text-eco-600\" />\n                </div>\n              </div>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <Button\n                  onClick={() => setActiveTab('deposit')}\n                  variant=\"outline\"\n                  className=\"h-12 text-base font-semibold rounded-xl\"\n                >\n                  <ArrowDownLeft className=\"h-5 w-5 mr-2\" />\n                  Deposit\n                </Button>\n                <Button\n                  onClick={() => setActiveTab('withdraw')}\n                  className=\"h-12 text-base font-semibold rounded-xl\"\n                  disabled={walletData.balance < 10}\n                >\n                  <ArrowUpRight className=\"h-5 w-5 mr-2\" />\n                  Withdraw\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Pending Earnings</p>\n                  <p className=\"text-4xl font-bold text-solar-600\">\n                    {formatCurrency(walletData.pendingEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Clock className=\"h-8 w-8 text-solar-600\" />\n                </div>\n              </div>\n              <p className=\"text-sm text-gray-600 font-medium\">\n                Will be transferred on Saturday at 15:00 UTC\n              </p>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Recent Transactions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Transactions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {walletData.recentTransactions.length > 0 ? (\n            <div className=\"space-y-3\">\n              {walletData.recentTransactions.map((transaction) => (\n                <div key={transaction.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    {getTransactionIcon(transaction.type)}\n                    <div>\n                      <p className=\"font-medium text-dark-900\">{transaction.description}</p>\n                      <p className=\"text-sm text-gray-500\">{formatDateTime(transaction.createdAt)}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(transaction.status)}\n                    <span className={`font-semibold ${getTransactionColor(transaction.type)}`}>\n                      {transaction.type === 'WITHDRAWAL' || transaction.type === 'PURCHASE' ? '-' : '+'}\n                      {formatCurrency(transaction.amount)}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No transactions yet</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Withdrawal History */}\n      {withdrawalHistory.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Withdrawal History</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {withdrawalHistory.map((withdrawal) => (\n                <div key={withdrawal.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-medium text-dark-900\">\n                        {formatCurrency(withdrawal.amount)}\n                      </span>\n                      {getStatusIcon(withdrawal.status)}\n                      <span className={`text-sm px-2 py-1 rounded-full ${\n                        withdrawal.status === 'COMPLETED' ? 'bg-eco-100 text-eco-700' :\n                        withdrawal.status === 'PENDING' ? 'bg-solar-100 text-solar-700' :\n                        'bg-red-100 text-red-700'\n                      }`}>\n                        {withdrawal.status}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <p className=\"text-sm text-gray-600\">\n                        To: {withdrawal.usdtAddress.slice(0, 8)}...{withdrawal.usdtAddress.slice(-8)}\n                      </p>\n                      <button\n                        onClick={() => handleCopyAddress(withdrawal.usdtAddress)}\n                        className=\"text-gray-400 hover:text-gray-600\"\n                      >\n                        <Copy className=\"h-3 w-3\" />\n                      </button>\n                    </div>\n                    <p className=\"text-xs text-gray-500\">{formatDateTime(withdrawal.createdAt)}</p>\n                    {withdrawal.txid && (\n                      <p className=\"text-xs text-eco-600 mt-1\">\n                        TXID: {withdrawal.txid.slice(0, 16)}...\n                      </p>\n                    )}\n                    {withdrawal.rejectionReason && (\n                      <p className=\"text-xs text-red-600 mt-1\">\n                        Reason: {withdrawal.rejectionReason}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n\n  const renderWithdrawContent = () => {\n    const amount = parseFloat(withdrawalForm.amount) || 0;\n    const feeCalculation = calculateWithdrawalFees(amount);\n\n    return (\n      <div className=\"space-y-8\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Withdraw USDT</h2>\n\n          <Card className=\"max-w-3xl\">\n            <CardContent className=\"p-8\">\n              <form onSubmit={handleWithdrawal} className=\"space-y-6\">\n                {withdrawalError && (\n                  <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n                    {withdrawalError}\n                  </div>\n                )}\n\n                <div className=\"bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg text-sm\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <div>\n                      <p className=\"font-medium\">Available Balance</p>\n                      <p className=\"text-lg font-bold\">{formatCurrency(walletData?.balance || 0)}</p>\n                    </div>\n                    <div>\n                      <p className=\"font-medium\">Minimum Withdrawal</p>\n                      <p className=\"text-lg font-bold\">{formatCurrency(withdrawalSettings?.minWithdrawalAmount || 10)}</p>\n                    </div>\n                    <div>\n                      <p className=\"font-medium\">Network</p>\n                      <p className=\"text-lg font-bold\">USDT (TRC20)</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  <div className=\"space-y-4\">\n                    <Input\n                      label=\"Withdrawal Amount (USD)\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min={withdrawalSettings?.minWithdrawalAmount || 10}\n                      max={walletData?.balance || 0}\n                      value={withdrawalForm.amount}\n                      onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}\n                      placeholder=\"Enter amount to withdraw\"\n                      required\n                    />\n\n                    <Input\n                      label=\"USDT TRC20 Address\"\n                      type=\"text\"\n                      value={withdrawalForm.usdtAddress}\n                      onChange={(e) => setWithdrawalForm(prev => ({ ...prev, usdtAddress: e.target.value }))}\n                      placeholder=\"Enter your USDT TRC20 address\"\n                      required\n                    />\n                  </div>\n\n                  {/* Fee Calculation Display */}\n                  {amount > 0 && (\n                    <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n                      <h4 className=\"font-medium text-gray-900 mb-3\">Fee Breakdown</h4>\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Withdrawal Amount:</span>\n                          <span className=\"font-medium\">{formatCurrency(amount)}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Fixed Fee:</span>\n                          <span className=\"font-medium\">{formatCurrency(feeCalculation.fixedFee)}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Percentage Fee ({withdrawalSettings?.percentageFee || 0}%):</span>\n                          <span className=\"font-medium\">{formatCurrency(feeCalculation.percentageFee)}</span>\n                        </div>\n                        <div className=\"border-t border-gray-300 pt-2\">\n                          <div className=\"flex justify-between font-medium\">\n                            <span className=\"text-gray-900\">Total Fees:</span>\n                            <span className=\"text-red-600\">{formatCurrency(feeCalculation.totalFees)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-medium\">\n                            <span className=\"text-gray-900\">Total Deduction:</span>\n                            <span className=\"text-red-600\">{formatCurrency(feeCalculation.totalDeduction)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-bold text-lg\">\n                            <span className=\"text-gray-900\">You Receive:</span>\n                            <span className=\"text-green-600\">{formatCurrency(feeCalculation.netAmount)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg text-sm\">\n                  <p><strong>Important:</strong></p>\n                  <ul className=\"list-disc list-inside mt-1 space-y-1\">\n                    <li>Only USDT TRC20 addresses are supported</li>\n                    <li>Withdrawals require KYC verification</li>\n                    <li>Processing time: {withdrawalSettings?.processingDays || 3} business days</li>\n                    <li>Double-check your address - transactions cannot be reversed</li>\n                    <li>Fees are deducted from your balance in addition to the withdrawal amount</li>\n                  </ul>\n                </div>\n\n                <div className=\"flex space-x-3\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    onClick={() => setActiveTab('overview')}\n                    className=\"flex-1\"\n                  >\n                    Back to Overview\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    loading={withdrawalLoading}\n                    className=\"flex-1\"\n                    disabled={\n                      !walletData ||\n                      !withdrawalSettings ||\n                      amount < (withdrawalSettings?.minWithdrawalAmount || 10) ||\n                      feeCalculation.totalDeduction > walletData.balance\n                    }\n                  >\n                    {feeCalculation.totalDeduction > (walletData?.balance || 0)\n                      ? 'Insufficient Balance'\n                      : 'Submit Withdrawal'\n                    }\n                  </Button>\n                </div>\n              </form>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Withdrawal History */}\n        {withdrawalHistory.length > 0 && (\n          <Card>\n            <CardHeader>\n              <CardTitle>Withdrawal History</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {withdrawalHistory.map((withdrawal) => (\n                  <div key={withdrawal.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"font-medium text-dark-900\">\n                          {formatCurrency(withdrawal.amount)}\n                        </span>\n                        {getStatusIcon(withdrawal.status)}\n                        <span className={`text-sm px-2 py-1 rounded-full ${\n                          withdrawal.status === 'COMPLETED' ? 'bg-eco-100 text-eco-700' :\n                          withdrawal.status === 'PENDING' ? 'bg-solar-100 text-solar-700' :\n                          'bg-red-100 text-red-700'\n                        }`}>\n                          {withdrawal.status}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center space-x-2 mt-1\">\n                        <p className=\"text-sm text-gray-600\">\n                          To: {withdrawal.usdtAddress.slice(0, 8)}...{withdrawal.usdtAddress.slice(-8)}\n                        </p>\n                        <button\n                          onClick={() => handleCopyAddress(withdrawal.usdtAddress)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <Copy className=\"h-3 w-3\" />\n                        </button>\n                      </div>\n                      <p className=\"text-xs text-gray-500\">{formatDateTime(withdrawal.createdAt)}</p>\n                      {withdrawal.txid && (\n                        <p className=\"text-xs text-eco-600 mt-1\">\n                          TXID: {withdrawal.txid.slice(0, 16)}...\n                        </p>\n                      )}\n                      {withdrawal.rejectionReason && (\n                        <p className=\"text-xs text-red-600 mt-1\">\n                          Reason: {withdrawal.rejectionReason}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            const isActive = activeTab === tab.id;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\n                  ${isActive\n                    ? 'border-solar-500 text-solar-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }\n                `}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {renderTabContent()}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAwCO,MAAM,kBAA4B;;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAClF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACxF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,QAAQ;QACR,aAAa;IACf;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YACA;YACA;QACF;oCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB,KAAK,IAAI;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,sBAAsB,KAAK,IAAI;gBACjC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAIA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,mBAAmB;QACnB,qBAAqB;QAErB,IAAI;YACF,MAAM,SAAS,WAAW,eAAe,MAAM;YAE/C,IAAI,CAAC,UAAU,UAAU,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,eAAe,WAAW,EAAE;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,aAAa,eAAe,WAAW;gBACzC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,6BAA6B;YAC7B,kBAAkB;gBAAE,QAAQ;gBAAI,aAAa;YAAG;YAChD,qBAAqB;YAErB,eAAe;YACf;YACA;QAEF,EAAE,OAAO,KAAU;YACjB,mBAAmB,IAAI,OAAO,IAAI;QACpC,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6NAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,+NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACtB,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YAClC,OAAO;gBAAE,UAAU;gBAAG,eAAe;gBAAG,WAAW;gBAAG,gBAAgB;gBAAG,WAAW;YAAE;QACxF;QAEA,MAAM,WAAW,mBAAmB,QAAQ;QAC5C,MAAM,gBAAgB,AAAC,SAAS,mBAAmB,aAAa,GAAI;QACpE,MAAM,YAAY,WAAW;QAC7B,MAAM,iBAAiB,SAAS;QAChC,MAAM,YAAY;QAElB,OAAO;YAAE;YAAU;YAAe;YAAW;YAAgB;QAAU;IACzE;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,yMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,+NAAA,CAAA,gBAAa;QAAC;QACvD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,6NAAA,CAAA,eAAY;QAAC;KACzD;IAED,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,cAAW;;;;;YACrB,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,kBAC5B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;8BACC,cAAA,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC;;;;;;sEACtD,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO;;;;;;;;;;;;8DAGtC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,SAAQ;oDACR,WAAU;;sEAEV,6LAAC,+NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG5C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,WAAU;oDACV,UAAU,WAAW,OAAO,GAAG;;sEAE/B,6LAAC,6NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAOjD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC;;;;;;sEACtD,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,eAAe;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGrB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASzD,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACT,WAAW,kBAAkB,CAAC,MAAM,GAAG,kBACtC,6LAAC;gCAAI,WAAU;0CACZ,WAAW,kBAAkB,CAAC,GAAG,CAAC,CAAC,4BAClC,6LAAC;wCAAyB,WAAU;;0DAClC,6LAAC;gDAAI,WAAU;;oDACZ,mBAAmB,YAAY,IAAI;kEACpC,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA6B,YAAY,WAAW;;;;;;0EACjE,6LAAC;gEAAE,WAAU;0EAAyB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,SAAS;;;;;;;;;;;;;;;;;;0DAG9E,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,YAAY,MAAM;kEACjC,6LAAC;wDAAK,WAAW,CAAC,cAAc,EAAE,oBAAoB,YAAY,IAAI,GAAG;;4DACtE,YAAY,IAAI,KAAK,gBAAgB,YAAY,IAAI,KAAK,aAAa,MAAM;4DAC7E,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;;;;;;;uCAZ9B,YAAY,EAAE;;;;;;;;;qDAmB5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;gBAOpC,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,6LAAC;wCAAwB,WAAU;kDACjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM;;;;;;wDAElC,cAAc,WAAW,MAAM;sEAChC,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAC/C,WAAW,MAAM,KAAK,cAAc,4BACpC,WAAW,MAAM,KAAK,YAAY,gCAClC,2BACA;sEACC,WAAW,MAAM;;;;;;;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;gEAAwB;gEAC9B,WAAW,WAAW,CAAC,KAAK,CAAC,GAAG;gEAAG;gEAAI,WAAW,WAAW,CAAC,KAAK,CAAC,CAAC;;;;;;;sEAE5E,6LAAC;4DACC,SAAS,IAAM,kBAAkB,WAAW,WAAW;4DACvD,WAAU;sEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGpB,6LAAC;oDAAE,WAAU;8DAAyB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,SAAS;;;;;;gDACxE,WAAW,IAAI,kBACd,6LAAC;oDAAE,WAAU;;wDAA4B;wDAChC,WAAW,IAAI,CAAC,KAAK,CAAC,GAAG;wDAAI;;;;;;;gDAGvC,WAAW,eAAe,kBACzB,6LAAC;oDAAE,WAAU;;wDAA4B;wDAC9B,WAAW,eAAe;;;;;;;;;;;;;uCAlCjC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+CrC,MAAM,wBAAwB;QAC5B,MAAM,SAAS,WAAW,eAAe,MAAM,KAAK;QACpD,MAAM,iBAAiB,wBAAwB;QAE/C,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAK,UAAU;oCAAkB,WAAU;;wCACzC,iCACC,6LAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAqB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,WAAW;;;;;;;;;;;;kEAE1E,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAqB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,uBAAuB;;;;;;;;;;;;kEAE9F,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAoB;;;;;;;;;;;;;;;;;;;;;;;sDAKvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,MAAK;4DACL,KAAK,oBAAoB,uBAAuB;4DAChD,KAAK,YAAY,WAAW;4DAC5B,OAAO,eAAe,MAAM;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC/E,aAAY;4DACZ,QAAQ;;;;;;sEAGV,6LAAC,oIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,OAAO,eAAe,WAAW;4DACjC,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACpF,aAAY;4DACZ,QAAQ;;;;;;;;;;;;gDAKX,SAAS,mBACR,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAK,WAAU;sFAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8EAEhD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAK,WAAU;sFAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,QAAQ;;;;;;;;;;;;8EAEvE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;gFAAgB;gFAAiB,oBAAoB,iBAAiB;gFAAE;;;;;;;sFACxF,6LAAC;4EAAK,WAAU;sFAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,aAAa;;;;;;;;;;;;8EAE5E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;;;;;;;;;;;;sFAEzE,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,cAAc;;;;;;;;;;;;sFAE9E,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAkB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQrF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAE,cAAA,6LAAC;kEAAO;;;;;;;;;;;8DACX,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;;gEAAG;gEAAkB,oBAAoB,kBAAkB;gEAAE;;;;;;;sEAC9D,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,aAAa;oDAC5B,WAAU;8DACX;;;;;;8DAGD,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,WAAU;oDACV,UACE,CAAC,cACD,CAAC,sBACD,SAAS,CAAC,oBAAoB,uBAAuB,EAAE,KACvD,eAAe,cAAc,GAAG,WAAW,OAAO;8DAGnD,eAAe,cAAc,GAAG,CAAC,YAAY,WAAW,CAAC,IACtD,yBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUf,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,6LAAC;wCAAwB,WAAU;kDACjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM;;;;;;wDAElC,cAAc,WAAW,MAAM;sEAChC,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAC/C,WAAW,MAAM,KAAK,cAAc,4BACpC,WAAW,MAAM,KAAK,YAAY,gCAClC,2BACA;sEACC,WAAW,MAAM;;;;;;;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;gEAAwB;gEAC9B,WAAW,WAAW,CAAC,KAAK,CAAC,GAAG;gEAAG;gEAAI,WAAW,WAAW,CAAC,KAAK,CAAC,CAAC;;;;;;;sEAE5E,6LAAC;4DACC,SAAS,IAAM,kBAAkB,WAAW,WAAW;4DACvD,WAAU;sEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGpB,6LAAC;oDAAE,WAAU;8DAAyB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,SAAS;;;;;;gDACxE,WAAW,IAAI,kBACd,6LAAC;oDAAE,WAAU;;wDAA4B;wDAChC,WAAW,IAAI,CAAC,KAAK,CAAC,GAAG;wDAAI;;;;;;;gDAGvC,WAAW,eAAe,kBACzB,6LAAC;oDAAE,WAAU;;wDAA4B;wDAC9B,WAAW,eAAe;;;;;;;;;;;;;uCAlCjC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8CvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBACrC,qBACE,6LAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC;;kBAEV,EAAE,WACE,oCACA,6EACH;gBACH,CAAC;;8CAED,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,IAAI,KAAK;;;;;;;2BAXX,IAAI,EAAE;;;;;oBAcjB;;;;;;;;;;;YAKH;;;;;;;AAGP;GA7lBa;KAAA", "debugId": null}}, {"offset": {"line": 5198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/BinaryPointsInfoPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  Clock, \n  TrendingUp, \n  AlertTriangle, \n  Info, \n  Award,\n  BarChart3,\n  Target,\n  Zap,\n  Calendar,\n  DollarSign\n} from 'lucide-react';\nimport { formatCurrency, formatNumber, getTimeUntilBinaryPayout } from '@/lib/utils';\n\ninterface BinaryPointsInfo {\n  currentPoints: {\n    left: number;\n    right: number;\n    leftCapped: number;\n    rightCapped: number;\n    matchable: number;\n    matched: number;\n    totalMatched: number;\n  };\n  limits: {\n    maxPointsPerSide: number;\n    pointValue: number;\n  };\n  progress: {\n    leftProgress: number;\n    rightProgress: number;\n    leftNearCap: boolean;\n    rightNearCap: boolean;\n  };\n  pressureOut: {\n    leftAmount: number;\n    rightAmount: number;\n    totalAmount: number;\n    willOccur: boolean;\n  };\n  earnings: {\n    estimatedPayout: number;\n    pointValue: number;\n    matchablePoints: number;\n  };\n  history: {\n    recentMatches: Array<{\n      amount: number;\n      date: string;\n      description: string;\n    }>;\n    averageWeeklyEarnings: number;\n  };\n  warnings: string[];\n  hasWarnings: boolean;\n  nextMatching: {\n    schedule: string;\n    description: string;\n  };\n}\n\nexport const BinaryPointsInfoPanel: React.FC = () => {\n  const [binaryInfo, setBinaryInfo] = useState<BinaryPointsInfo | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeUntilMatching, setTimeUntilMatching] = useState(getTimeUntilBinaryPayout());\n\n  useEffect(() => {\n    fetchBinaryInfo();\n    \n    // Update countdown every second\n    const interval = setInterval(() => {\n      setTimeUntilMatching(getTimeUntilBinaryPayout());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchBinaryInfo = async () => {\n    try {\n      const response = await fetch('/api/binary-points/info', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setBinaryInfo(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch binary points info:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 3 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!binaryInfo) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load binary points information</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const ProgressBar = ({ value, max, color = 'bg-green-500', warningThreshold = 90 }: {\n    value: number;\n    max: number;\n    color?: string;\n    warningThreshold?: number;\n  }) => {\n    const percentage = (value / max) * 100;\n    const isWarning = percentage >= warningThreshold;\n    \n    return (\n      <div className=\"w-full bg-gray-200 rounded-full h-3\">\n        <div \n          className={`h-3 rounded-full transition-all duration-300 ${\n            isWarning ? 'bg-red-500' : color\n          }`}\n          style={{ width: `${Math.min(percentage, 100)}%` }}\n        />\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Warnings Section */}\n      {binaryInfo.hasWarnings && (\n        <Card className=\"border-orange-200 bg-orange-50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2 text-orange-800\">\n              <AlertTriangle className=\"h-5 w-5\" />\n              Important Notices\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"space-y-2\">\n              {binaryInfo.warnings.map((warning, index) => (\n                <li key={index} className=\"flex items-start gap-2 text-orange-700\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0\" />\n                  <span className=\"text-sm\">{warning}</span>\n                </li>\n              ))}\n            </ul>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Current Binary Points Status */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <BarChart3 className=\"h-5 w-5 text-green-600\" />\n            Current Binary Points Status\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Grid cols={{ default: 1, md: 2 }} gap={6}>\n            {/* Left Side */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-gray-700\">Left Side Points</span>\n                <span className=\"text-lg font-bold text-green-600\">\n                  {formatNumber(binaryInfo.currentPoints.left, 0)}\n                </span>\n              </div>\n              <ProgressBar \n                value={binaryInfo.currentPoints.leftCapped} \n                max={binaryInfo.limits.maxPointsPerSide}\n                color=\"bg-green-500\"\n              />\n              <div className=\"flex justify-between text-xs text-gray-500\">\n                <span>0</span>\n                <span>{formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} (cap)</span>\n              </div>\n              {binaryInfo.pressureOut.leftAmount > 0 && (\n                <p className=\"text-xs text-red-600\">\n                  {formatNumber(binaryInfo.pressureOut.leftAmount, 0)} points will be flushed\n                </p>\n              )}\n            </div>\n\n            {/* Right Side */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-gray-700\">Right Side Points</span>\n                <span className=\"text-lg font-bold text-blue-600\">\n                  {formatNumber(binaryInfo.currentPoints.right, 0)}\n                </span>\n              </div>\n              <ProgressBar \n                value={binaryInfo.currentPoints.rightCapped} \n                max={binaryInfo.limits.maxPointsPerSide}\n                color=\"bg-blue-500\"\n              />\n              <div className=\"flex justify-between text-xs text-gray-500\">\n                <span>0</span>\n                <span>{formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} (cap)</span>\n              </div>\n              {binaryInfo.pressureOut.rightAmount > 0 && (\n                <p className=\"text-xs text-red-600\">\n                  {formatNumber(binaryInfo.pressureOut.rightAmount, 0)} points will be flushed\n                </p>\n              )}\n            </div>\n          </Grid>\n\n          {/* Matchable Points */}\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <Target className=\"h-5 w-5 text-purple-600\" />\n                <span className=\"font-medium text-gray-900\">Points Available for Matching</span>\n              </div>\n              <span className=\"text-2xl font-bold text-purple-600\">\n                {formatNumber(binaryInfo.currentPoints.matchable, 0)}\n              </span>\n            </div>\n            <p className=\"text-sm text-gray-600 mt-2\">\n              Minimum of left ({formatNumber(binaryInfo.currentPoints.leftCapped, 0)}) and right ({formatNumber(binaryInfo.currentPoints.rightCapped, 0)}) sides\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Next Matching Cycle */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Clock className=\"h-5 w-5 text-blue-600\" />\n            Next Weekly Binary Matching\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600 mb-4\">\n              {binaryInfo.nextMatching.schedule}\n            </p>\n            <div className=\"grid grid-cols-4 gap-4 mb-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {timeUntilMatching.days}\n                </div>\n                <div className=\"text-xs text-gray-500\">Days</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {timeUntilMatching.hours}\n                </div>\n                <div className=\"text-xs text-gray-500\">Hours</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {timeUntilMatching.minutes}\n                </div>\n                <div className=\"text-xs text-gray-500\">Minutes</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {timeUntilMatching.seconds}\n                </div>\n                <div className=\"text-xs text-gray-500\">Seconds</div>\n              </div>\n            </div>\n            <p className=\"text-xs text-gray-500\">\n              {binaryInfo.nextMatching.description}\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Earnings Estimation */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <DollarSign className=\"h-5 w-5 text-green-600\" />\n            Earnings Estimation\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n              <span className=\"font-medium text-green-800\">Estimated Weekly Payout</span>\n              <span className=\"text-xl font-bold text-green-600\">\n                {formatCurrency(binaryInfo.earnings.estimatedPayout)}\n              </span>\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"text-gray-600\">Point Value:</span>\n                <div className=\"font-semibold\">{formatCurrency(binaryInfo.earnings.pointValue)} per point</div>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Your Matchable Points:</span>\n                <div className=\"font-semibold\">{formatNumber(binaryInfo.earnings.matchablePoints, 0)}</div>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Estimated Payout:</span>\n                <div className=\"font-semibold\">{formatCurrency(binaryInfo.earnings.estimatedPayout)}</div>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Max Points Per Side:</span>\n                <div className=\"font-semibold\">{formatNumber(binaryInfo.limits.maxPointsPerSide, 0)}</div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Historical Performance */}\n      {binaryInfo.history.recentMatches.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n              Recent Binary Matching History\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-purple-50 rounded-lg\">\n                <span className=\"font-medium text-purple-800\">Average Weekly Earnings</span>\n                <span className=\"text-lg font-bold text-purple-600\">\n                  {formatCurrency(binaryInfo.history.averageWeeklyEarnings)}\n                </span>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium text-gray-900\">Last 4 Weeks</h4>\n                {binaryInfo.history.recentMatches.map((match, index) => (\n                  <div key={index} className=\"flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0\">\n                    <div>\n                      <div className=\"font-medium\">{formatCurrency(match.amount)}</div>\n                      <div className=\"text-xs text-gray-500\">\n                        {new Date(match.date).toLocaleDateString()}\n                      </div>\n                    </div>\n                    <div className=\"text-xs text-gray-600 max-w-xs text-right\">\n                      {match.description}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Binary Network Education */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Info className=\"h-5 w-5 text-blue-600\" />\n            How Binary Matching Works\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4 text-sm text-gray-700\">\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">Point Accumulation</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• Every $1 invested by your downline = 1 binary point</li>\n                <li>• Points are added to left or right side based on placement</li>\n                <li>• Maximum {formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} points per side</li>\n              </ul>\n            </div>\n            \n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">Weekly Matching Process</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• Matching occurs every Saturday at 15:00 UTC</li>\n                <li>• Matched points = minimum of left and right sides</li>\n                <li>• Fixed earnings: {formatCurrency(binaryInfo.limits.pointValue)} per matched point</li>\n                <li>• Excess points beyond cap are flushed (pressure-out)</li>\n              </ul>\n            </div>\n            \n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">Pressure-Out System</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• Points exceeding {formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} per side are automatically flushed</li>\n                <li>• Encourages balanced team building on both sides</li>\n                <li>• Prevents point hoarding and ensures fair distribution</li>\n              </ul>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAjBA;;;;;;AAkEO,MAAM,wBAAkC;;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;IAElF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR;YAEA,gCAAgC;YAChC,MAAM,WAAW;4DAAY;oBAC3B,qBAAqB,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;gBAC9C;2DAAG;YAEH;mDAAO,IAAM,cAAc;;QAC7B;0CAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,cAAc,EAAE,mBAAmB,EAAE,EAK/E;QACC,MAAM,aAAa,AAAC,QAAQ,MAAO;QACnC,MAAM,YAAY,cAAc;QAEhC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,WAAW,CAAC,6CAA6C,EACvD,YAAY,eAAe,OAC3B;gBACF,OAAO;oBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC;gBAAC;;;;;;;;;;;IAIxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,WAAW,WAAW,kBACrB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIzC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAG,WAAU;sCACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjC,6LAAC;oCAAe,WAAU;;sDACxB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;mCAFpB;;;;;;;;;;;;;;;;;;;;;0BAWnB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAIpD,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC,uIAAA,CAAA,OAAI;gCAAC,MAAM;oCAAE,SAAS;oCAAG,IAAI;gCAAE;gCAAG,KAAK;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,aAAa,CAAC,IAAI,EAAE;;;;;;;;;;;;0DAGjD,6LAAC;gDACC,OAAO,WAAW,aAAa,CAAC,UAAU;gDAC1C,KAAK,WAAW,MAAM,CAAC,gBAAgB;gDACvC,OAAM;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,MAAM,CAAC,gBAAgB,EAAE;4DAAG;;;;;;;;;;;;;4CAE5D,WAAW,WAAW,CAAC,UAAU,GAAG,mBACnC,6LAAC;gDAAE,WAAU;;oDACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,WAAW,CAAC,UAAU,EAAE;oDAAG;;;;;;;;;;;;;kDAM1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,aAAa,CAAC,KAAK,EAAE;;;;;;;;;;;;0DAGlD,6LAAC;gDACC,OAAO,WAAW,aAAa,CAAC,WAAW;gDAC3C,KAAK,WAAW,MAAM,CAAC,gBAAgB;gDACvC,OAAM;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,MAAM,CAAC,gBAAgB,EAAE;4DAAG;;;;;;;;;;;;;4CAE5D,WAAW,WAAW,CAAC,WAAW,GAAG,mBACpC,6LAAC;gDAAE,WAAU;;oDACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,WAAW,CAAC,WAAW,EAAE;oDAAG;;;;;;;;;;;;;;;;;;;0CAO7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,aAAa,CAAC,SAAS,EAAE;;;;;;;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;;4CAA6B;4CACtB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,aAAa,CAAC,UAAU,EAAE;4CAAG;4CAAc,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,aAAa,CAAC,WAAW,EAAE;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnJ,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI/C,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,WAAW,YAAY,CAAC,QAAQ;;;;;;8CAEnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,kBAAkB,IAAI;;;;;;8DAEzB,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,kBAAkB,KAAK;;;;;;8DAE1B,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,kBAAkB,OAAO;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,kBAAkB,OAAO;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAG3C,6LAAC;oCAAE,WAAU;8CACV,WAAW,YAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAIrD,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;sDAC7C,6LAAC;4CAAK,WAAU;sDACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,CAAC,eAAe;;;;;;;;;;;;8CAIvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAI,WAAU;;wDAAiB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,CAAC,UAAU;wDAAE;;;;;;;;;;;;;sDAEjF,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAI,WAAU;8DAAiB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,QAAQ,CAAC,eAAe,EAAE;;;;;;;;;;;;sDAEpF,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAI,WAAU;8DAAiB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,CAAC,eAAe;;;;;;;;;;;;sDAEpF,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAI,WAAU;8DAAiB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,MAAM,CAAC,gBAAgB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ1F,WAAW,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,mBACzC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAItD,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;sDAC9C,6LAAC;4CAAK,WAAU;sDACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,CAAC,qBAAqB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;wCACzC,WAAW,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC5C,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;;;;;;0EACzD,6LAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;;;;;;;;;;;;kEAG5C,6LAAC;wDAAI,WAAU;kEACZ,MAAM,WAAW;;;;;;;+CARZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmBtB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI9C,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;;wDAAG;wDAAW,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,MAAM,CAAC,gBAAgB,EAAE;wDAAG;;;;;;;;;;;;;;;;;;;8CAIvE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;;wDAAG;wDAAmB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM,CAAC,UAAU;wDAAE;;;;;;;8DACpE,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;;wDAAG;wDAAoB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,MAAM,CAAC,gBAAgB,EAAE;wDAAG;;;;;;;8DAC5E,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAzVa;KAAA", "debugId": null}}, {"offset": {"line": 6381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/D3BinaryTree.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Button } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  Users, Copy, Share2, TrendingUp, <PERSON>, RefreshCw, Eye, EyeOff,\n  ZoomIn, ZoomOut, Maximize2\n} from 'lucide-react';\nimport { formatCurrency, formatDate, copyToClipboard } from '@/lib/utils';\nimport { BinaryPointsInfoPanel } from './BinaryPointsInfoPanel';\nimport { hierarchy, tree } from 'd3-hierarchy';\nimport { select } from 'd3-selection';\nimport { zoom, zoomIdentity } from 'd3-zoom';\nimport 'd3-transition';\n\ninterface BinaryTreeNode {\n  user: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    createdAt: string;\n    isActive: boolean;\n  };\n  sponsorInfo?: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n  directReferralCount: number;\n  teamCounts: {\n    left: number;\n    right: number;\n    total: number;\n  };\n  binaryPoints: {\n    leftPoints: number;\n    rightPoints: number;\n    matchedPoints: number;\n  };\n  hasLeftChild?: boolean;\n  hasRightChild?: boolean;\n  leftChild?: BinaryTreeNode;\n  rightChild?: BinaryTreeNode;\n}\n\ninterface BinaryTreeData {\n  treeStructure: BinaryTreeNode;\n  statistics: {\n    totalDirectReferrals: number;\n    leftReferrals: number;\n    rightReferrals: number;\n    totalCommissions: number;\n    binaryPoints: {\n      leftPoints: number;\n      rightPoints: number;\n      matchedPoints: number;\n    };\n  };\n  referralLinks: {\n    left: string;\n    right: string;\n    general: string;\n  };\n}\n\ninterface D3TreeNode extends d3.HierarchyNode<BinaryTreeNode> {\n  _children?: D3TreeNode[];\n  x0?: number;\n  y0?: number;\n}\n\n// Import d3 namespace for types\nimport * as d3 from 'd3-hierarchy';\n\nexport const D3BinaryTree: React.FC = () => {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [treeData, setTreeData] = useState<BinaryTreeData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());\n  const [showInactive, setShowInactive] = useState(true);\n  \n  // D3 tree layout configuration\n  const margin = { top: 50, right: 120, bottom: 50, left: 120 };\n  const width = 1200 - margin.left - margin.right;\n  const height = 800 - margin.bottom - margin.top;\n  const nodeWidth = 200;\n  const nodeHeight = 120;\n\n  const fetchTreeData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/referrals/tree?depth=5&enhanced=true&expanded=${Array.from(expandedNodes).join(',')}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setTreeData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch binary tree data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Convert binary tree structure to D3 hierarchy format with expand/collapse support\n  const convertToD3Hierarchy = useCallback((node: BinaryTreeNode): d3.HierarchyNode<BinaryTreeNode> => {\n    const hierarchyNode = hierarchy(node, (d) => {\n      // Check if this node should show its children\n      const isExpanded = expandedNodes.has(d.user.id);\n\n      // Root node (first level) is always expanded, others depend on expanded state\n      const shouldShowChildren = d === node || isExpanded;\n\n      if (!shouldShowChildren) {\n        return null; // Don't show children if node is collapsed\n      }\n\n      const nodeChildren: BinaryTreeNode[] = [];\n\n      // Add left child if it exists and should be visible\n      if (d.leftChild && (showInactive || d.leftChild.user.isActive)) {\n        nodeChildren.push(d.leftChild);\n      }\n\n      // Add right child if it exists and should be visible\n      if (d.rightChild && (showInactive || d.rightChild.user.isActive)) {\n        nodeChildren.push(d.rightChild);\n      }\n      return nodeChildren.length > 0 ? nodeChildren : null;\n    });\n\n    return hierarchyNode;\n  }, [showInactive, expandedNodes]);\n\n  // Initialize and render the D3 tree\n  const renderTree = useCallback(() => {\n    if (!treeData || !svgRef.current) return;\n\n    try {\n      const svg = select(svgRef.current);\n      svg.selectAll(\"*\").remove(); // Clear previous render\n\n      // Create main group for zoom/pan\n      const container = svg\n        .attr(\"width\", width + margin.left + margin.right)\n        .attr(\"height\", height + margin.top + margin.bottom);\n\n      const g = container\n        .append(\"g\")\n        .attr(\"class\", \"tree-container\")\n        .attr(\"transform\", `translate(${margin.left},${margin.top})`);\n\n      // Setup zoom behavior\n      const zoomBehavior = zoom<SVGSVGElement, unknown>()\n        .scaleExtent([0.1, 3])\n        .on(\"zoom\", (event) => {\n          g.attr(\"transform\", event.transform);\n        });\n\n      svg.call(zoomBehavior);\n\n      // Create tree layout\n      const treeLayout = tree<BinaryTreeNode>()\n        .size([width, height])\n        .nodeSize([nodeWidth + 50, nodeHeight + 80]) // Add spacing between nodes\n        .separation((a, b) => {\n          // Increase separation for better spacing\n          return a.parent === b.parent ? 2 : 3;\n        });\n\n      // Convert data to hierarchy and apply layout\n      const root = convertToD3Hierarchy(treeData.treeStructure);\n      const treeRoot = treeLayout(root) as D3TreeNode;\n\n      // Center the tree\n      const nodes = treeRoot.descendants();\n      const links = treeRoot.links();\n\n      // Adjust positions to center the tree\n      let minX = Infinity;\n      let maxX = -Infinity;\n      nodes.forEach(d => {\n        if (d.x < minX) minX = d.x;\n        if (d.x > maxX) maxX = d.x;\n      });\n      const centerOffset = (width / 2) - ((minX + maxX) / 2);\n      nodes.forEach(d => {\n        d.x += centerOffset;\n      });\n\n    // Render links (connections between nodes)\n    const linkGroup = g.append(\"g\").attr(\"class\", \"links\");\n    \n    linkGroup.selectAll(\".link\")\n      .data(links)\n      .enter()\n      .append(\"path\")\n      .attr(\"class\", \"link\")\n      .attr(\"d\", (d: any) => {\n        const source = d.source;\n        const target = d.target;\n        \n        // Create smooth curved path\n        return `M${source.x},${source.y + nodeHeight/2}\n                C${source.x},${(source.y + target.y) / 2}\n                 ${target.x},${(source.y + target.y) / 2}\n                 ${target.x},${target.y - nodeHeight/2}`;\n      })\n      .style(\"fill\", \"none\")\n      .style(\"stroke\", \"#94a3b8\")\n      .style(\"stroke-width\", \"2px\")\n      .style(\"stroke-opacity\", 0.6);\n\n    // Render nodes\n    const nodeGroup = g.append(\"g\").attr(\"class\", \"nodes\");\n    \n    const nodeEnter = nodeGroup.selectAll(\".node\")\n      .data(nodes)\n      .enter()\n      .append(\"g\")\n      .attr(\"class\", \"node\")\n      .attr(\"transform\", (d: any) => `translate(${d.x - nodeWidth/2},${d.y - nodeHeight/2})`)\n      .style(\"cursor\", \"pointer\");\n\n    // Add node background (card)\n    nodeEnter\n      .append(\"rect\")\n      .attr(\"width\", nodeWidth)\n      .attr(\"height\", nodeHeight)\n      .attr(\"rx\", 12)\n      .attr(\"ry\", 12)\n      .style(\"fill\", \"white\")\n      .style(\"stroke\", (d: any) => {\n        // Different border colors based on state\n        if (d.data.user.isActive) {\n          return (d.data.hasLeftChild || d.data.hasRightChild) && !expandedNodes.has(d.data.user.id)\n            ? \"#3b82f6\" // Blue border for collapsed nodes with children\n            : \"#10b981\"; // Green border for active nodes\n        }\n        return \"#6b7280\"; // Gray border for inactive nodes\n      })\n      .style(\"stroke-width\", (d: any) => {\n        // Thicker border for nodes with collapsed children\n        return (d.data.hasLeftChild || d.data.hasRightChild) && !expandedNodes.has(d.data.user.id) ? 3 : 2;\n      })\n      .style(\"filter\", \"drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))\");\n\n    // Add avatar circle\n    nodeEnter\n      .append(\"circle\")\n      .attr(\"cx\", nodeWidth / 2)\n      .attr(\"cy\", 25)\n      .attr(\"r\", 18)\n      .style(\"fill\", (d: any) => d.data.user.isActive ? \"#10b981\" : \"#6b7280\");\n\n    // Add avatar text (initials)\n    nodeEnter\n      .append(\"text\")\n      .attr(\"x\", nodeWidth / 2)\n      .attr(\"y\", 25)\n      .attr(\"dy\", \"0.35em\")\n      .style(\"text-anchor\", \"middle\")\n      .style(\"fill\", \"white\")\n      .style(\"font-weight\", \"bold\")\n      .style(\"font-size\", \"12px\")\n      .text((d: any) => {\n        const firstName = d.data.user.firstName || '';\n        const lastName = d.data.user.lastName || '';\n        return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();\n      });\n\n    // Add name text\n    nodeEnter\n      .append(\"text\")\n      .attr(\"x\", nodeWidth / 2)\n      .attr(\"y\", 55)\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-weight\", \"600\")\n      .style(\"font-size\", \"13px\")\n      .style(\"fill\", \"#1f2937\")\n      .text((d: any) => `${d.data.user.firstName} ${d.data.user.lastName}`);\n\n    // Add status badge\n    nodeEnter\n      .append(\"rect\")\n      .attr(\"x\", nodeWidth / 2 - 25)\n      .attr(\"y\", 65)\n      .attr(\"width\", 50)\n      .attr(\"height\", 16)\n      .attr(\"rx\", 8)\n      .style(\"fill\", (d: any) => d.data.user.isActive ? \"#dcfce7\" : \"#f3f4f6\")\n      .style(\"stroke\", (d: any) => d.data.user.isActive ? \"#16a34a\" : \"#6b7280\")\n      .style(\"stroke-width\", 1);\n\n    nodeEnter\n      .append(\"text\")\n      .attr(\"x\", nodeWidth / 2)\n      .attr(\"y\", 73)\n      .attr(\"dy\", \"0.35em\")\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"10px\")\n      .style(\"font-weight\", \"500\")\n      .style(\"fill\", (d: any) => d.data.user.isActive ? \"#16a34a\" : \"#6b7280\")\n      .text((d: any) => d.data.user.isActive ? \"Active\" : \"Inactive\");\n\n    // Add sponsor info if available\n    nodeEnter\n      .filter((d: any) => d.data.sponsorInfo)\n      .append(\"text\")\n      .attr(\"x\", nodeWidth / 2)\n      .attr(\"y\", 90)\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"9px\")\n      .style(\"fill\", \"#3b82f6\")\n      .text((d: any) => `Sponsor: ${d.data.sponsorInfo.firstName} ${d.data.sponsorInfo.lastName}`);\n\n    // Add team counts\n    nodeEnter\n      .append(\"text\")\n      .attr(\"x\", nodeWidth / 2)\n      .attr(\"y\", 105)\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"10px\")\n      .style(\"font-weight\", \"500\")\n      .style(\"fill\", \"#4b5563\")\n      .text((d: any) => `Team: ${d.data.teamCounts.total} (L:${d.data.teamCounts.left} R:${d.data.teamCounts.right})`);\n\n    // Add expand/collapse functionality for nodes with children\n    const expandableNodes = nodeEnter.filter((d: any) => d.data.hasLeftChild || d.data.hasRightChild);\n\n    // Add expand/collapse button background\n    expandableNodes\n      .append(\"circle\")\n      .attr(\"cx\", nodeWidth - 15)\n      .attr(\"cy\", 15)\n      .attr(\"r\", 10)\n      .style(\"fill\", (d: any) => expandedNodes.has(d.data.user.id) ? \"#ef4444\" : \"#3b82f6\")\n      .style(\"stroke\", \"white\")\n      .style(\"stroke-width\", 2)\n      .style(\"cursor\", \"pointer\")\n      .style(\"transition\", \"all 0.2s ease\")\n      .style(\"transform-origin\", \"center\")\n      .on(\"click\", function(event, d: any) {\n        event.stopPropagation();\n        handleNodeToggle(d.data.user.id);\n      })\n      .on(\"mouseover\", function() {\n        select(this)\n          .transition()\n          .duration(150)\n          .attr(\"r\", 11)\n          .style(\"filter\", \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\");\n      })\n      .on(\"mouseout\", function() {\n        select(this)\n          .transition()\n          .duration(150)\n          .attr(\"r\", 10)\n          .style(\"filter\", \"none\");\n      });\n\n    // Add expand/collapse button text\n    expandableNodes\n      .append(\"text\")\n      .attr(\"x\", nodeWidth - 15)\n      .attr(\"y\", 15)\n      .attr(\"dy\", \"0.35em\")\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"12px\")\n      .style(\"font-weight\", \"bold\")\n      .style(\"fill\", \"white\")\n      .style(\"cursor\", \"pointer\")\n      .style(\"pointer-events\", \"none\") // Let clicks pass through to circle\n      .text((d: any) => expandedNodes.has(d.data.user.id) ? \"−\" : \"+\");\n\n    // Add zoom controls\n    const controls = container.append(\"g\")\n      .attr(\"class\", \"zoom-controls\")\n      .attr(\"transform\", `translate(${width + margin.left - 100}, 20)`);\n\n    // Zoom in button\n    const zoomInBtn = controls.append(\"g\")\n      .style(\"cursor\", \"pointer\")\n      .on(\"click\", () => {\n        svg.transition().duration(300).call(\n          zoomBehavior.scaleBy, 1.5\n        );\n      });\n\n    zoomInBtn.append(\"rect\")\n      .attr(\"width\", 30)\n      .attr(\"height\", 30)\n      .attr(\"rx\", 4)\n      .style(\"fill\", \"white\")\n      .style(\"stroke\", \"#d1d5db\")\n      .style(\"stroke-width\", 1);\n\n    zoomInBtn.append(\"text\")\n      .attr(\"x\", 15)\n      .attr(\"y\", 15)\n      .attr(\"dy\", \"0.35em\")\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"16px\")\n      .style(\"font-weight\", \"bold\")\n      .text(\"+\");\n\n    // Zoom out button\n    const zoomOutBtn = controls.append(\"g\")\n      .attr(\"transform\", \"translate(0, 35)\")\n      .style(\"cursor\", \"pointer\")\n      .on(\"click\", () => {\n        svg.transition().duration(300).call(\n          zoomBehavior.scaleBy, 0.67\n        );\n      });\n\n    zoomOutBtn.append(\"rect\")\n      .attr(\"width\", 30)\n      .attr(\"height\", 30)\n      .attr(\"rx\", 4)\n      .style(\"fill\", \"white\")\n      .style(\"stroke\", \"#d1d5db\")\n      .style(\"stroke-width\", 1);\n\n    zoomOutBtn.append(\"text\")\n      .attr(\"x\", 15)\n      .attr(\"y\", 15)\n      .attr(\"dy\", \"0.35em\")\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"16px\")\n      .style(\"font-weight\", \"bold\")\n      .text(\"−\");\n\n    // Reset zoom button\n    const resetBtn = controls.append(\"g\")\n      .attr(\"transform\", \"translate(0, 70)\")\n      .style(\"cursor\", \"pointer\")\n      .on(\"click\", () => {\n        svg.transition().duration(500).call(\n          zoomBehavior.transform,\n          zoomIdentity\n        );\n      });\n\n    resetBtn.append(\"rect\")\n      .attr(\"width\", 30)\n      .attr(\"height\", 30)\n      .attr(\"rx\", 4)\n      .style(\"fill\", \"white\")\n      .style(\"stroke\", \"#d1d5db\")\n      .style(\"stroke-width\", 1);\n\n    resetBtn.append(\"text\")\n      .attr(\"x\", 15)\n      .attr(\"y\", 15)\n      .attr(\"dy\", \"0.35em\")\n      .style(\"text-anchor\", \"middle\")\n      .style(\"font-size\", \"12px\")\n      .style(\"font-weight\", \"bold\")\n      .text(\"⌂\");\n\n    } catch (error) {\n      console.error('Error rendering D3 tree:', error);\n    }\n  }, [treeData, expandedNodes, showInactive, convertToD3Hierarchy]);\n\n  const handleNodeToggle = useCallback((nodeId: string) => {\n    setExpandedNodes(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(nodeId)) {\n        newSet.delete(nodeId);\n      } else {\n        newSet.add(nodeId);\n      }\n      return newSet;\n    });\n  }, []);\n\n  const handleCopyLink = async (link: string) => {\n    try {\n      await copyToClipboard(link);\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchTreeData();\n  }, [expandedNodes]);\n\n  useEffect(() => {\n    if (treeData) {\n      renderTree();\n    }\n  }, [treeData, renderTree, expandedNodes]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <RefreshCw className=\"w-8 h-8 animate-spin text-blue-400\" />\n      </div>\n    );\n  }\n\n  if (!treeData) {\n    return (\n      <div className=\"text-center py-12\">\n        <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Tree Data</h3>\n        <p className=\"text-gray-500\">Unable to load your binary tree structure.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Direct Referrals</p>\n                <p className=\"text-2xl font-bold\">{treeData.statistics.totalDirectReferrals}</p>\n              </div>\n              <Users className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Left Side</p>\n                <p className=\"text-lg font-bold text-green-600\">{treeData.statistics.leftReferrals || 0} users</p>\n                <p className=\"text-sm text-green-500\">{treeData.statistics.binaryPoints.leftPoints} points</p>\n              </div>\n              <TrendingUp className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Right Side</p>\n                <p className=\"text-lg font-bold text-orange-600\">{treeData.statistics.rightReferrals || 0} users</p>\n                <p className=\"text-sm text-orange-500\">{treeData.statistics.binaryPoints.rightPoints} points</p>\n              </div>\n              <TrendingUp className=\"h-8 w-8 text-orange-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Binary Points</p>\n                <p className=\"text-lg font-bold text-purple-600\">{treeData.statistics.binaryPoints.matchedPoints} matched</p>\n                <p className=\"text-sm text-purple-500\">\n                  {Math.min(treeData.statistics.binaryPoints.leftPoints, treeData.statistics.binaryPoints.rightPoints)} available\n                </p>\n              </div>\n              <Award className=\"h-8 w-8 text-purple-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Tree Visualization */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <span>Binary Tree Structure</span>\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={() => setShowInactive(!showInactive)}\n              >\n                {showInactive ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                {showInactive ? 'Hide Inactive' : 'Show Inactive'}\n              </Button>\n              <Button size=\"sm\" onClick={fetchTreeData}>\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Refresh\n              </Button>\n            </div>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"border border-gray-200 rounded-lg overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50\">\n            <svg\n              ref={svgRef}\n              className=\"w-full\"\n              style={{ height: '700px' }}\n            />\n          </div>\n          <div className=\"mt-4 text-sm text-gray-600 space-y-1\">\n            <p>• <strong>Zoom:</strong> Use mouse wheel or zoom controls</p>\n            <p>• <strong>Pan:</strong> Click and drag to move around</p>\n            <p>• <strong>Expand/Collapse:</strong> Click the + or - button on nodes</p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Referral Links */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Share2 className=\"h-5 w-5 text-blue-500\" />\n            <span>Referral Links</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"text-sm font-medium text-gray-700\">General Referral Link</label>\n              <div className=\"flex mt-1\">\n                <input\n                  type=\"text\"\n                  value={treeData.referralLinks.general}\n                  readOnly\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                />\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleCopyLink(treeData.referralLinks.general)}\n                  className=\"rounded-l-none\"\n                >\n                  <Copy className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium text-gray-700\">Left Side Link</label>\n              <div className=\"flex mt-1\">\n                <input\n                  type=\"text\"\n                  value={treeData.referralLinks.left}\n                  readOnly\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                />\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleCopyLink(treeData.referralLinks.left)}\n                  className=\"rounded-l-none\"\n                >\n                  <Copy className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium text-gray-700\">Right Side Link</label>\n              <div className=\"flex mt-1\">\n                <input\n                  type=\"text\"\n                  value={treeData.referralLinks.right}\n                  readOnly\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                />\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleCopyLink(treeData.referralLinks.right)}\n                  className=\"rounded-l-none\"\n                >\n                  <Copy className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-4 space-y-3\">\n            <div className=\"p-3 bg-blue-50 rounded-lg\">\n              <p className=\"text-xs text-blue-700\">\n                <strong>Enhanced Placement:</strong> New users are automatically placed in your weaker leg\n                for optimal network balance. Use specific side links to target placement.\n              </p>\n            </div>\n            <div className=\"p-3 bg-green-50 rounded-lg\">\n              <p className=\"text-xs text-green-700\">\n                <strong>Binary Matching:</strong> Points are matched weekly on Saturdays at 15:00 UTC.\n                Each $1 investment by your downline = 1 binary point. Maximum 2,000 points per side.\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Binary Points Information Panel */}\n      <BinaryPointsInfoPanel />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;AAdA;;;;;;;;;;AA6EO,MAAM,eAAyB;;IACpC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,+BAA+B;IAC/B,MAAM,SAAS;QAAE,KAAK;QAAI,OAAO;QAAK,QAAQ;QAAI,MAAM;IAAI;IAC5D,MAAM,QAAQ,OAAO,OAAO,IAAI,GAAG,OAAO,KAAK;IAC/C,MAAM,SAAS,MAAM,OAAO,MAAM,GAAG,OAAO,GAAG;IAC/C,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,mDAAmD,EAAE,MAAM,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM,EAAE;gBACxH,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI;gBACvB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF;IAEA,oFAAoF;IACpF,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACxC,MAAM,gBAAgB,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD,EAAE;gFAAM,CAAC;oBACrC,8CAA8C;oBAC9C,MAAM,aAAa,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE;oBAE9C,8EAA8E;oBAC9E,MAAM,qBAAqB,MAAM,QAAQ;oBAEzC,IAAI,CAAC,oBAAoB;wBACvB,OAAO,MAAM,2CAA2C;oBAC1D;oBAEA,MAAM,eAAiC,EAAE;oBAEzC,oDAAoD;oBACpD,IAAI,EAAE,SAAS,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG;wBAC9D,aAAa,IAAI,CAAC,EAAE,SAAS;oBAC/B;oBAEA,qDAAqD;oBACrD,IAAI,EAAE,UAAU,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG;wBAChE,aAAa,IAAI,CAAC,EAAE,UAAU;oBAChC;oBACA,OAAO,aAAa,MAAM,GAAG,IAAI,eAAe;gBAClD;;YAEA,OAAO;QACT;yDAAG;QAAC;QAAc;KAAc;IAEhC,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC7B,IAAI,CAAC,YAAY,CAAC,OAAO,OAAO,EAAE;YAElC,IAAI;gBACF,MAAM,MAAM,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,OAAO,OAAO;gBACjC,IAAI,SAAS,CAAC,KAAK,MAAM,IAAI,wBAAwB;gBAErD,iCAAiC;gBACjC,MAAM,YAAY,IACf,IAAI,CAAC,SAAS,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK,EAChD,IAAI,CAAC,UAAU,SAAS,OAAO,GAAG,GAAG,OAAO,MAAM;gBAErD,MAAM,IAAI,UACP,MAAM,CAAC,KACP,IAAI,CAAC,SAAS,kBACd,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;gBAE9D,sBAAsB;gBACtB,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,OAAI,AAAD,IACrB,WAAW,CAAC;oBAAC;oBAAK;iBAAE,EACpB,EAAE,CAAC;yEAAQ,CAAC;wBACX,EAAE,IAAI,CAAC,aAAa,MAAM,SAAS;oBACrC;;gBAEF,IAAI,IAAI,CAAC;gBAET,qBAAqB;gBACrB,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,IACnB,IAAI,CAAC;oBAAC;oBAAO;iBAAO,EACpB,QAAQ,CAAC;oBAAC,YAAY;oBAAI,aAAa;iBAAG,EAAE,4BAA4B;iBACxE,UAAU;uEAAC,CAAC,GAAG;wBACd,yCAAyC;wBACzC,OAAO,EAAE,MAAM,KAAK,EAAE,MAAM,GAAG,IAAI;oBACrC;;gBAEF,6CAA6C;gBAC7C,MAAM,OAAO,qBAAqB,SAAS,aAAa;gBACxD,MAAM,WAAW,WAAW;gBAE5B,kBAAkB;gBAClB,MAAM,QAAQ,SAAS,WAAW;gBAClC,MAAM,QAAQ,SAAS,KAAK;gBAE5B,sCAAsC;gBACtC,IAAI,OAAO;gBACX,IAAI,OAAO,CAAC;gBACZ,MAAM,OAAO;4DAAC,CAAA;wBACZ,IAAI,EAAE,CAAC,GAAG,MAAM,OAAO,EAAE,CAAC;wBAC1B,IAAI,EAAE,CAAC,GAAG,MAAM,OAAO,EAAE,CAAC;oBAC5B;;gBACA,MAAM,eAAe,AAAC,QAAQ,IAAM,CAAC,OAAO,IAAI,IAAI;gBACpD,MAAM,OAAO;4DAAC,CAAA;wBACZ,EAAE,CAAC,IAAI;oBACT;;gBAEF,2CAA2C;gBAC3C,MAAM,YAAY,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS;gBAE9C,UAAU,SAAS,CAAC,SACjB,IAAI,CAAC,OACL,KAAK,GACL,MAAM,CAAC,QACP,IAAI,CAAC,SAAS,QACd,IAAI,CAAC;4DAAK,CAAC;wBACV,MAAM,SAAS,EAAE,MAAM;wBACvB,MAAM,SAAS,EAAE,MAAM;wBAEvB,4BAA4B;wBAC5B,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,aAAW,EAAE;iBACtC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE;iBACxC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE;iBACxC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,aAAW,GAAG;oBAClD;2DACC,KAAK,CAAC,QAAQ,QACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,gBAAgB,OACtB,KAAK,CAAC,kBAAkB;gBAE3B,eAAe;gBACf,MAAM,YAAY,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS;gBAE9C,MAAM,YAAY,UAAU,SAAS,CAAC,SACnC,IAAI,CAAC,OACL,KAAK,GACL,MAAM,CAAC,KACP,IAAI,CAAC,SAAS,QACd,IAAI,CAAC;sEAAa,CAAC,IAAW,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,YAAU,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,aAAW,EAAE,CAAC,CAAC;qEACrF,KAAK,CAAC,UAAU;gBAEnB,6BAA6B;gBAC7B,UACG,MAAM,CAAC,QACP,IAAI,CAAC,SAAS,WACd,IAAI,CAAC,UAAU,YACf,IAAI,CAAC,MAAM,IACX,IAAI,CAAC,MAAM,IACX,KAAK,CAAC,QAAQ,SACd,KAAK,CAAC;4DAAU,CAAC;wBAChB,yCAAyC;wBACzC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BACxB,OAAO,CAAC,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IACrF,UAAU,gDAAgD;+BAC1D,WAAW,gCAAgC;wBACjD;wBACA,OAAO,WAAW,iCAAiC;oBACrD;2DACC,KAAK,CAAC;4DAAgB,CAAC;wBACtB,mDAAmD;wBACnD,OAAO,CAAC,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI;oBACnG;2DACC,KAAK,CAAC,UAAU;gBAEnB,oBAAoB;gBACpB,UACG,MAAM,CAAC,UACP,IAAI,CAAC,MAAM,YAAY,GACvB,IAAI,CAAC,MAAM,IACX,IAAI,CAAC,KAAK,IACV,KAAK,CAAC;4DAAQ,CAAC,IAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY;;gBAEhE,6BAA6B;gBAC7B,UACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,GACtB,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,MAAM,UACX,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,QAAQ,SACd,KAAK,CAAC,eAAe,QACrB,KAAK,CAAC,aAAa,QACnB,IAAI;4DAAC,CAAC;wBACL,MAAM,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI;wBAC3C,MAAM,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI;wBACzC,OAAO,GAAG,SAAS,CAAC,EAAE,IAAI,KAAK,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW;oBAChE;;gBAEF,gBAAgB;gBAChB,UACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,GACtB,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,eAAe,OACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,QAAQ,WACd,IAAI;4DAAC,CAAC,IAAW,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;gBAEtE,mBAAmB;gBACnB,UACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,IAAI,IAC1B,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,SAAS,IACd,IAAI,CAAC,UAAU,IACf,IAAI,CAAC,MAAM,GACX,KAAK,CAAC;4DAAQ,CAAC,IAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY;2DAC7D,KAAK,CAAC;4DAAU,CAAC,IAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY;2DAC/D,KAAK,CAAC,gBAAgB;gBAEzB,UACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,GACtB,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,MAAM,UACX,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,eAAe,OACrB,KAAK,CAAC;4DAAQ,CAAC,IAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY;2DAC7D,IAAI;4DAAC,CAAC,IAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,WAAW;;gBAEtD,gCAAgC;gBAChC,UACG,MAAM;4DAAC,CAAC,IAAW,EAAE,IAAI,CAAC,WAAW;2DACrC,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,GACtB,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,OACnB,KAAK,CAAC,QAAQ,WACd,IAAI;4DAAC,CAAC,IAAW,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;;gBAE7F,kBAAkB;gBAClB,UACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,GACtB,IAAI,CAAC,KAAK,KACV,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,eAAe,OACrB,KAAK,CAAC,QAAQ,WACd,IAAI;4DAAC,CAAC,IAAW,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;;gBAEjH,4DAA4D;gBAC5D,MAAM,kBAAkB,UAAU,MAAM;4EAAC,CAAC,IAAW,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,IAAI,CAAC,aAAa;;gBAEhG,wCAAwC;gBACxC,gBACG,MAAM,CAAC,UACP,IAAI,CAAC,MAAM,YAAY,IACvB,IAAI,CAAC,MAAM,IACX,IAAI,CAAC,KAAK,IACV,KAAK,CAAC;4DAAQ,CAAC,IAAW,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,YAAY;2DAC1E,KAAK,CAAC,UAAU,SAChB,KAAK,CAAC,gBAAgB,GACtB,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,cAAc,iBACpB,KAAK,CAAC,oBAAoB,UAC1B,EAAE,CAAC;4DAAS,SAAS,KAAK,EAAE,CAAM;wBACjC,MAAM,eAAe;wBACrB,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjC;2DACC,EAAE,CAAC;4DAAa;wBACf,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EACR,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,UAAU;oBACrB;2DACC,EAAE,CAAC;4DAAY;wBACd,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EACR,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,UAAU;oBACrB;;gBAEF,kCAAkC;gBAClC,gBACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,YAAY,IACtB,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,MAAM,UACX,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,eAAe,QACrB,KAAK,CAAC,QAAQ,SACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,kBAAkB,QAAQ,oCAAoC;iBACpE,IAAI;4DAAC,CAAC,IAAW,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM;;gBAE9D,oBAAoB;gBACpB,MAAM,WAAW,UAAU,MAAM,CAAC,KAC/B,IAAI,CAAC,SAAS,iBACd,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC;gBAElE,iBAAiB;gBACjB,MAAM,YAAY,SAAS,MAAM,CAAC,KAC/B,KAAK,CAAC,UAAU,WAChB,EAAE,CAAC;sEAAS;wBACX,IAAI,UAAU,GAAG,QAAQ,CAAC,KAAK,IAAI,CACjC,aAAa,OAAO,EAAE;oBAE1B;;gBAEF,UAAU,MAAM,CAAC,QACd,IAAI,CAAC,SAAS,IACd,IAAI,CAAC,UAAU,IACf,IAAI,CAAC,MAAM,GACX,KAAK,CAAC,QAAQ,SACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,gBAAgB;gBAEzB,UAAU,MAAM,CAAC,QACd,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,MAAM,UACX,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,eAAe,QACrB,IAAI,CAAC;gBAER,kBAAkB;gBAClB,MAAM,aAAa,SAAS,MAAM,CAAC,KAChC,IAAI,CAAC,aAAa,oBAClB,KAAK,CAAC,UAAU,WAChB,EAAE,CAAC;uEAAS;wBACX,IAAI,UAAU,GAAG,QAAQ,CAAC,KAAK,IAAI,CACjC,aAAa,OAAO,EAAE;oBAE1B;;gBAEF,WAAW,MAAM,CAAC,QACf,IAAI,CAAC,SAAS,IACd,IAAI,CAAC,UAAU,IACf,IAAI,CAAC,MAAM,GACX,KAAK,CAAC,QAAQ,SACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,gBAAgB;gBAEzB,WAAW,MAAM,CAAC,QACf,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,MAAM,UACX,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,eAAe,QACrB,IAAI,CAAC;gBAER,oBAAoB;gBACpB,MAAM,WAAW,SAAS,MAAM,CAAC,KAC9B,IAAI,CAAC,aAAa,oBAClB,KAAK,CAAC,UAAU,WAChB,EAAE,CAAC;qEAAS;wBACX,IAAI,UAAU,GAAG,QAAQ,CAAC,KAAK,IAAI,CACjC,aAAa,SAAS,EACtB,6LAAA,CAAA,eAAY;oBAEhB;;gBAEF,SAAS,MAAM,CAAC,QACb,IAAI,CAAC,SAAS,IACd,IAAI,CAAC,UAAU,IACf,IAAI,CAAC,MAAM,GACX,KAAK,CAAC,QAAQ,SACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,gBAAgB;gBAEzB,SAAS,MAAM,CAAC,QACb,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,KAAK,IACV,IAAI,CAAC,MAAM,UACX,KAAK,CAAC,eAAe,UACrB,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,eAAe,QACrB,IAAI,CAAC;YAER,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;+CAAG;QAAC;QAAU;QAAe;QAAc;KAAqB;IAEhE,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACpC;8DAAiB,CAAA;oBACf,MAAM,SAAS,IAAI,IAAI;oBACvB,IAAI,OAAO,GAAG,CAAC,SAAS;wBACtB,OAAO,MAAM,CAAC;oBAChB,OAAO;wBACL,OAAO,GAAG,CAAC;oBACb;oBACA,OAAO;gBACT;;QACF;qDAAG,EAAE;IAEL,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAc;IAElB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;iCAAG;QAAC;QAAU;QAAY;KAAc;IAExC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAsB,SAAS,UAAU,CAAC,oBAAoB;;;;;;;;;;;;kDAE7E,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAoC,SAAS,UAAU,CAAC,aAAa,IAAI;oDAAE;;;;;;;0DACxF,6LAAC;gDAAE,WAAU;;oDAA0B,SAAS,UAAU,CAAC,YAAY,CAAC,UAAU;oDAAC;;;;;;;;;;;;;kDAErF,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAqC,SAAS,UAAU,CAAC,cAAc,IAAI;oDAAE;;;;;;;0DAC1F,6LAAC;gDAAE,WAAU;;oDAA2B,SAAS,UAAU,CAAC,YAAY,CAAC,WAAW;oDAAC;;;;;;;;;;;;;kDAEvF,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAqC,SAAS,UAAU,CAAC,YAAY,CAAC,aAAa;oDAAC;;;;;;;0DACjG,6LAAC;gDAAE,WAAU;;oDACV,KAAK,GAAG,CAAC,SAAS,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,UAAU,CAAC,YAAY,CAAC,WAAW;oDAAE;;;;;;;;;;;;;kDAGzG,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,gBAAgB,CAAC;;gDAE/B,6BAAe,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;yEAAe,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAC/D,eAAe,kBAAkB;;;;;;;sDAEpC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAS;;8DACzB,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,WAAU;oCACV,OAAO;wCAAE,QAAQ;oCAAQ;;;;;;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAc;;;;;;;kDAC3B,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAa;;;;;;;kDAC1B,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,aAAa,CAAC,OAAO;wDACrC,QAAQ;wDACR,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,eAAe,SAAS,aAAa,CAAC,OAAO;wDAC5D,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKtB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,aAAa,CAAC,IAAI;wDAClC,QAAQ;wDACR,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,eAAe,SAAS,aAAa,CAAC,IAAI;wDACzD,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKtB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,aAAa,CAAC,KAAK;wDACnC,QAAQ;wDACR,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,eAAe,SAAS,aAAa,CAAC,KAAK;wDAC1D,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAO;;;;;;gDAA4B;;;;;;;;;;;;kDAIxC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAO;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,6LAAC,2JAAA,CAAA,wBAAqB;;;;;;;;;;;AAG5B;GApnBa;KAAA", "debugId": null}}, {"offset": {"line": 7485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/KYCPortal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent, Button, Input } from '@/components/ui';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Shield, Upload, CheckCircle, XCircle, Clock, AlertCircle, FileText, Camera } from 'lucide-react';\nimport { DocumentType, IDType, DocumentSide } from '@/types';\n\ninterface KYCDocument {\n  id: string;\n  documentType: DocumentType;\n  idType?: IDType;\n  documentSide?: DocumentSide;\n  filePath: string;\n  status: 'PENDING' | 'APPROVED' | 'REJECTED';\n  reviewedAt?: string;\n  rejectionReason?: string;\n  createdAt: string;\n}\n\ninterface KYCSubmission {\n  idType: IDType;\n  documents: {\n    [key: string]: File;\n  };\n  isComplete: boolean;\n}\n\nexport const KYCPortal: React.FC = () => {\n  const { user, refreshUser } = useAuth();\n  const [documents, setDocuments] = useState<KYCDocument[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [uploading, setUploading] = useState(false);\n  const [uploadError, setUploadError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n\n  // Enhanced KYC flow state\n  const [selectedIdType, setSelectedIdType] = useState<IDType | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File}>({});\n  const [step, setStep] = useState<'select' | 'upload' | 'review'>('select');\n\n  useEffect(() => {\n    fetchKYCDocuments();\n  }, []);\n\n  const fetchKYCDocuments = async () => {\n    try {\n      const response = await fetch('/api/kyc/documents', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setDocuments(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch KYC documents:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFileSelect = (documentKey: string, file: File) => {\n    // Validate file\n    if (!file.type.startsWith('image/')) {\n      setUploadError('Please upload an image file');\n      return;\n    }\n\n    if (file.size > 5 * 1024 * 1024) { // 5MB limit\n      setUploadError('File size must be less than 5MB');\n      return;\n    }\n\n    setUploadError('');\n    setUploadedFiles(prev => ({\n      ...prev,\n      [documentKey]: file\n    }));\n  };\n\n  const getRequiredDocuments = (idType: IDType): string[] => {\n    const docs = ['selfie'];\n\n    if (idType === IDType.PASSPORT) {\n      docs.push('id_front');\n    } else {\n      docs.push('id_front', 'id_back');\n    }\n\n    return docs;\n  };\n\n  const isSubmissionComplete = (): boolean => {\n    if (!selectedIdType) return false;\n\n    const requiredDocs = getRequiredDocuments(selectedIdType);\n    return requiredDocs.every(doc => uploadedFiles[doc]);\n  };\n\n  const handleSubmitKYC = async () => {\n    if (!selectedIdType || !isSubmissionComplete()) {\n      setUploadError('Please complete all required uploads');\n      return;\n    }\n\n    setSubmitting(true);\n    setUploadError('');\n\n    try {\n      const requiredDocs = getRequiredDocuments(selectedIdType);\n\n      for (const docKey of requiredDocs) {\n        const file = uploadedFiles[docKey];\n        if (!file) continue;\n\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('idType', selectedIdType);\n\n        if (docKey === 'selfie') {\n          formData.append('documentType', DocumentType.SELFIE);\n        } else {\n          formData.append('documentType', DocumentType.ID_DOCUMENT);\n          formData.append('documentSide', docKey === 'id_front' ? DocumentSide.FRONT : DocumentSide.BACK);\n        }\n\n        const response = await fetch('/api/kyc/upload', {\n          method: 'POST',\n          credentials: 'include',\n          body: formData,\n        });\n\n        const data = await response.json();\n        if (!data.success) {\n          throw new Error(data.error || 'Upload failed');\n        }\n      }\n\n      // Refresh documents and user data\n      await fetchKYCDocuments();\n      await refreshUser();\n\n      // Reset form\n      setUploadedFiles({});\n      setSelectedIdType(null);\n      setStep('select');\n\n    } catch (err: any) {\n      setUploadError(err.message || 'Submission failed');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'APPROVED':\n        return <CheckCircle className=\"h-5 w-5 text-eco-500\" />;\n      case 'REJECTED':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />;\n      case 'PENDING':\n        return <Clock className=\"h-5 w-5 text-solar-500\" />;\n      default:\n        return <AlertCircle className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'APPROVED':\n        return 'bg-eco-100 text-eco-700';\n      case 'REJECTED':\n        return 'bg-red-100 text-red-700';\n      case 'PENDING':\n        return 'bg-solar-100 text-solar-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  const getDocumentByType = (type: DocumentType, side?: DocumentSide) => {\n    return documents.find(doc =>\n      doc.documentType === type &&\n      (side ? doc.documentSide === side : true)\n    );\n  };\n\n  const hasExistingKYC = () => {\n    return documents.length > 0;\n  };\n\n  const getIdTypeLabel = (idType: IDType): string => {\n    switch (idType) {\n      case IDType.NATIONAL_ID:\n        return 'National ID';\n      case IDType.PASSPORT:\n        return 'Passport';\n      case IDType.DRIVING_LICENSE:\n        return 'Driving License';\n      default:\n        return '';\n    }\n  };\n\n  // ID Type Selection Component\n  const IDTypeSelection: React.FC = () => (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <FileText className=\"h-5 w-5 text-solar-500\" />\n          <span>Select ID Document Type</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <p className=\"text-sm text-gray-600 mb-6\">\n          Choose the type of government-issued ID you want to upload for verification.\n        </p>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {Object.values(IDType).map((idType) => (\n            <button\n              key={idType}\n              onClick={() => {\n                setSelectedIdType(idType);\n                setStep('upload');\n              }}\n              className=\"p-4 border-2 border-gray-200 rounded-lg hover:border-solar-500 hover:bg-solar-50 transition-colors text-left\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <FileText className=\"h-6 w-6 text-solar-500\" />\n                <div>\n                  <h3 className=\"font-medium text-dark-900\">{getIdTypeLabel(idType)}</h3>\n                  <p className=\"text-xs text-gray-500\">\n                    {idType === IDType.PASSPORT ? 'Front side only' : 'Front and back required'}\n                  </p>\n                </div>\n              </div>\n            </button>\n          ))}\n        </div>\n      </CardContent>\n    </Card>\n  );\n\n  // Document Upload Component\n  const DocumentUpload: React.FC<{\n    documentKey: string;\n    title: string;\n    description: string;\n    required: boolean;\n  }> = ({ documentKey, title, description, required }) => {\n    const inputId = `file-${documentKey}`;\n    const hasFile = uploadedFiles[documentKey];\n\n    return (\n      <div className=\"border border-gray-200 rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-dark-900\">\n              {title} {required && <span className=\"text-red-500\">*</span>}\n            </h3>\n            <p className=\"text-sm text-gray-600\">{description}</p>\n          </div>\n          {hasFile && (\n            <CheckCircle className=\"h-5 w-5 text-eco-500\" />\n          )}\n        </div>\n\n        {hasFile ? (\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <img\n                src={URL.createObjectURL(hasFile)}\n                alt={title}\n                className=\"w-16 h-16 object-cover rounded-lg\"\n              />\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-dark-900\">{hasFile.name}</p>\n                <p className=\"text-xs text-gray-500\">\n                  {(hasFile.size / 1024 / 1024).toFixed(2)} MB\n                </p>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  setUploadedFiles(prev => {\n                    const newFiles = { ...prev };\n                    delete newFiles[documentKey];\n                    return newFiles;\n                  });\n                }}\n              >\n                Remove\n              </Button>\n            </div>\n          </div>\n        ) : (\n          <div>\n            <input\n              id={inputId}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={(e) => {\n                const file = e.target.files?.[0];\n                if (file) {\n                  handleFileSelect(documentKey, file);\n                }\n              }}\n              className=\"hidden\"\n            />\n            <label\n              htmlFor={inputId}\n              className=\"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100\"\n            >\n              <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                <Upload className=\"h-8 w-8 text-gray-400 mb-2\" />\n                <p className=\"text-sm text-gray-500\">\n                  <span className=\"font-semibold\">Click to upload</span> or drag and drop\n                </p>\n                <p className=\"text-xs text-gray-500\">PNG, JPG up to 5MB</p>\n              </div>\n            </label>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Shield className=\"h-5 w-5 text-solar-500\" />\n            <span>KYC Verification</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"animate-pulse space-y-4\">\n            <div className=\"h-32 bg-gray-200 rounded-lg\"></div>\n            <div className=\"h-32 bg-gray-200 rounded-lg\"></div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* KYC Status Overview */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Shield className=\"h-5 w-5 text-solar-500\" />\n            <span>KYC Verification Status</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center space-x-4\">\n            {getStatusIcon(user?.kycStatus || 'PENDING')}\n            <div>\n              <p className=\"text-lg font-semibold text-dark-900\">\n                Status: <span className={`${\n                  user?.kycStatus === 'APPROVED' ? 'text-eco-600' :\n                  user?.kycStatus === 'REJECTED' ? 'text-red-600' :\n                  'text-solar-600'\n                }`}>\n                  {user?.kycStatus || 'PENDING'}\n                </span>\n              </p>\n              <p className=\"text-sm text-gray-600\">\n                {user?.kycStatus === 'APPROVED' && 'Your identity has been verified. You can now make withdrawals.'}\n                {user?.kycStatus === 'PENDING' && 'Your documents are being reviewed. This usually takes 1-3 business days.'}\n                {user?.kycStatus === 'REJECTED' && 'Your verification was rejected. Please re-upload your documents.'}\n              </p>\n            </div>\n          </div>\n\n          {user?.kycStatus !== 'APPROVED' && (\n            <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-900 mb-2\">Why do we need KYC verification?</h4>\n              <ul className=\"text-sm text-blue-700 space-y-1\">\n                <li>• Comply with international financial regulations</li>\n                <li>• Protect your account from unauthorized access</li>\n                <li>• Enable secure withdrawals to your wallet</li>\n                <li>• Prevent fraud and money laundering</li>\n              </ul>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Enhanced KYC Flow */}\n      {user?.kycStatus !== 'APPROVED' && (\n        <>\n          {/* Show existing documents if any */}\n          {hasExistingKYC() && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Current Documents</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {documents.map((doc) => (\n                    <div key={doc.id} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n                      <img\n                        src={doc.filePath}\n                        alt={`${doc.documentType} document`}\n                        className=\"w-16 h-16 object-cover rounded-lg\"\n                      />\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-dark-900\">\n                          {doc.documentType === DocumentType.SELFIE ? 'Selfie Photo' :\n                           `${getIdTypeLabel(doc.idType!)} - ${doc.documentSide}`}\n                        </p>\n                        <div className=\"flex items-center space-x-2 mt-1\">\n                          {getStatusIcon(doc.status)}\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(doc.status)}`}>\n                            {doc.status}\n                          </span>\n                        </div>\n                        {doc.rejectionReason && (\n                          <p className=\"text-xs text-red-600 mt-1\">\n                            Rejection reason: {doc.rejectionReason}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* New KYC Submission Flow */}\n          {(user?.kycStatus === 'REJECTED' || !hasExistingKYC()) && (\n            <>\n              {step === 'select' && <IDTypeSelection />}\n\n              {step === 'upload' && selectedIdType && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center justify-between\">\n                      <span>Upload {getIdTypeLabel(selectedIdType)} Documents</span>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => {\n                          setStep('select');\n                          setSelectedIdType(null);\n                          setUploadedFiles({});\n                        }}\n                      >\n                        Change ID Type\n                      </Button>\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    {uploadError && (\n                      <div className=\"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n                        {uploadError}\n                      </div>\n                    )}\n\n                    <div className=\"space-y-6\">\n                      {/* ID Document Front */}\n                      <DocumentUpload\n                        documentKey=\"id_front\"\n                        title={`${getIdTypeLabel(selectedIdType)} - Front`}\n                        description=\"Upload a clear photo of the front side of your ID\"\n                        required={true}\n                      />\n\n                      {/* ID Document Back (only for non-passport) */}\n                      {selectedIdType !== IDType.PASSPORT && (\n                        <DocumentUpload\n                          documentKey=\"id_back\"\n                          title={`${getIdTypeLabel(selectedIdType)} - Back`}\n                          description=\"Upload a clear photo of the back side of your ID\"\n                          required={true}\n                        />\n                      )}\n\n                      {/* Selfie */}\n                      <DocumentUpload\n                        documentKey=\"selfie\"\n                        title=\"Selfie with ID\"\n                        description=\"Take a selfie holding your ID document next to your face\"\n                        required={true}\n                      />\n                    </div>\n\n                    {/* Submit Button */}\n                    <div className=\"mt-6 flex justify-end space-x-3\">\n                      <Button\n                        variant=\"outline\"\n                        onClick={() => {\n                          setStep('select');\n                          setSelectedIdType(null);\n                          setUploadedFiles({});\n                        }}\n                        disabled={submitting}\n                      >\n                        Cancel\n                      </Button>\n                      <Button\n                        onClick={handleSubmitKYC}\n                        disabled={!isSubmissionComplete() || submitting}\n                        loading={submitting}\n                      >\n                        Submit KYC Documents\n                      </Button>\n                    </div>\n\n                    {/* Requirements */}\n                    <div className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                      <h4 className=\"text-sm font-medium text-yellow-900 mb-2\">Document Requirements:</h4>\n                      <ul className=\"text-sm text-yellow-700 space-y-1\">\n                        <li>• Documents must be clear and readable</li>\n                        <li>• All four corners of the ID must be visible</li>\n                        <li>• No blurred, cropped, or edited images</li>\n                        <li>• Selfie must clearly show your face and the ID</li>\n                        <li>• File formats: JPG, PNG (max 5MB each)</li>\n                      </ul>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AA4BO,MAAM,YAAsB;;IACjC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,aAAa,KAAK,IAAI;gBACxB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC,aAAqB;QAC7C,gBAAgB;QAChB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,eAAe;YACf;QACF;QAEA,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,eAAe;YACf;QACF;QAEA,eAAe;QACf,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,YAAY,EAAE;YACjB,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO;YAAC;SAAS;QAEvB,IAAI,WAAW,wHAAA,CAAA,SAAM,CAAC,QAAQ,EAAE;YAC9B,KAAK,IAAI,CAAC;QACZ,OAAO;YACL,KAAK,IAAI,CAAC,YAAY;QACxB;QAEA,OAAO;IACT;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,gBAAgB,OAAO;QAE5B,MAAM,eAAe,qBAAqB;QAC1C,OAAO,aAAa,KAAK,CAAC,CAAA,MAAO,aAAa,CAAC,IAAI;IACrD;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,kBAAkB,CAAC,wBAAwB;YAC9C,eAAe;YACf;QACF;QAEA,cAAc;QACd,eAAe;QAEf,IAAI;YACF,MAAM,eAAe,qBAAqB;YAE1C,KAAK,MAAM,UAAU,aAAc;gBACjC,MAAM,OAAO,aAAa,CAAC,OAAO;gBAClC,IAAI,CAAC,MAAM;gBAEX,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBACxB,SAAS,MAAM,CAAC,UAAU;gBAE1B,IAAI,WAAW,UAAU;oBACvB,SAAS,MAAM,CAAC,gBAAgB,wHAAA,CAAA,eAAY,CAAC,MAAM;gBACrD,OAAO;oBACL,SAAS,MAAM,CAAC,gBAAgB,wHAAA,CAAA,eAAY,CAAC,WAAW;oBACxD,SAAS,MAAM,CAAC,gBAAgB,WAAW,aAAa,wHAAA,CAAA,eAAY,CAAC,KAAK,GAAG,wHAAA,CAAA,eAAY,CAAC,IAAI;gBAChG;gBAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,aAAa;oBACb,MAAM;gBACR;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;YACF;YAEA,kCAAkC;YAClC,MAAM;YACN,MAAM;YAEN,aAAa;YACb,iBAAiB,CAAC;YAClB,kBAAkB;YAClB,QAAQ;QAEV,EAAE,OAAO,KAAU;YACjB,eAAe,IAAI,OAAO,IAAI;QAChC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC,MAAoB;QAC7C,OAAO,UAAU,IAAI,CAAC,CAAA,MACpB,IAAI,YAAY,KAAK,QACrB,CAAC,OAAO,IAAI,YAAY,KAAK,OAAO,IAAI;IAE5C;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,GAAG;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,wHAAA,CAAA,SAAM,CAAC,WAAW;gBACrB,OAAO;YACT,KAAK,wHAAA,CAAA,SAAM,CAAC,QAAQ;gBAClB,OAAO;YACT,KAAK,wHAAA,CAAA,SAAM,CAAC,eAAe;gBACzB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,8BAA8B;IAC9B,MAAM,kBAA4B,kBAChC,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;;sCACV,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;sCACZ,OAAO,MAAM,CAAC,wHAAA,CAAA,SAAM,EAAE,GAAG,CAAC,CAAC,uBAC1B,6LAAC;oCAEC,SAAS;wCACP,kBAAkB;wCAClB,QAAQ;oCACV;oCACA,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,eAAe;;;;;;kEAC1D,6LAAC;wDAAE,WAAU;kEACV,WAAW,wHAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,oBAAoB;;;;;;;;;;;;;;;;;;mCAZnD;;;;;;;;;;;;;;;;;;;;;;IAuBjB,4BAA4B;IAC5B,MAAM,iBAKD,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE;QACjD,MAAM,UAAU,CAAC,KAAK,EAAE,aAAa;QACrC,MAAM,UAAU,aAAa,CAAC,YAAY;QAE1C,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCACX;wCAAM;wCAAE,0BAAY,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;wBAEvC,yBACC,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;gBAI1B,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK,IAAI,eAAe,CAAC;gCACzB,KAAK;gCACL,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqC,QAAQ,IAAI;;;;;;kDAC9D,6LAAC;wCAAE,WAAU;;4CACV,CAAC,QAAQ,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAG7C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP,iBAAiB,CAAA;wCACf,MAAM,WAAW;4CAAE,GAAG,IAAI;wCAAC;wCAC3B,OAAO,QAAQ,CAAC,YAAY;wCAC5B,OAAO;oCACT;gCACF;0CACD;;;;;;;;;;;;;;;;yCAML,6LAAC;;sCACC,6LAAC;4BACC,IAAI;4BACJ,MAAK;4BACL,QAAO;4BACP,UAAU,CAAC;gCACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gCAChC,IAAI,MAAM;oCACR,iBAAiB,aAAa;gCAChC;4BACF;4BACA,WAAU;;;;;;sCAEZ,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAsB;;;;;;;kDAExD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,MAAM,aAAa;kDAClC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;;oDAAsC;kEACzC,6LAAC;wDAAK,WAAW,GACvB,MAAM,cAAc,aAAa,iBACjC,MAAM,cAAc,aAAa,iBACjC,kBACA;kEACC,MAAM,aAAa;;;;;;;;;;;;0DAGxB,6LAAC;gDAAE,WAAU;;oDACV,MAAM,cAAc,cAAc;oDAClC,MAAM,cAAc,aAAa;oDACjC,MAAM,cAAc,cAAc;;;;;;;;;;;;;;;;;;;4BAKxC,MAAM,cAAc,4BACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQb,MAAM,cAAc,4BACnB;;oBAEG,kCACC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,oBACd,6LAAC;4CAAiB,WAAU;;8DAC1B,6LAAC;oDACC,KAAK,IAAI,QAAQ;oDACjB,KAAK,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC;oDACnC,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,IAAI,YAAY,KAAK,wHAAA,CAAA,eAAY,CAAC,MAAM,GAAG,iBAC3C,GAAG,eAAe,IAAI,MAAM,EAAG,GAAG,EAAE,IAAI,YAAY,EAAE;;;;;;sEAEzD,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,IAAI,MAAM;8EACzB,6LAAC;oEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,IAAI,MAAM,GAAG;8EACxF,IAAI,MAAM;;;;;;;;;;;;wDAGd,IAAI,eAAe,kBAClB,6LAAC;4DAAE,WAAU;;gEAA4B;gEACpB,IAAI,eAAe;;;;;;;;;;;;;;2CAnBpC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;oBA+BzB,CAAC,MAAM,cAAc,cAAc,CAAC,gBAAgB,mBACnD;;4BACG,SAAS,0BAAY,6LAAC;;;;;4BAEtB,SAAS,YAAY,gCACpB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;;wDAAK;wDAAQ,eAAe;wDAAgB;;;;;;;8DAC7C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,QAAQ;wDACR,kBAAkB;wDAClB,iBAAiB,CAAC;oDACpB;8DACD;;;;;;;;;;;;;;;;;kDAKL,6LAAC,mIAAA,CAAA,cAAW;;4CACT,6BACC,6LAAC;gDAAI,WAAU;0DACZ;;;;;;0DAIL,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDACC,aAAY;wDACZ,OAAO,GAAG,eAAe,gBAAgB,QAAQ,CAAC;wDAClD,aAAY;wDACZ,UAAU;;;;;;oDAIX,mBAAmB,wHAAA,CAAA,SAAM,CAAC,QAAQ,kBACjC,6LAAC;wDACC,aAAY;wDACZ,OAAO,GAAG,eAAe,gBAAgB,OAAO,CAAC;wDACjD,aAAY;wDACZ,UAAU;;;;;;kEAKd,6LAAC;wDACC,aAAY;wDACZ,OAAM;wDACN,aAAY;wDACZ,UAAU;;;;;;;;;;;;0DAKd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS;4DACP,QAAQ;4DACR,kBAAkB;4DAClB,iBAAiB,CAAC;wDACpB;wDACA,UAAU;kEACX;;;;;;kEAGD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU,CAAC,0BAA0B;wDACrC,SAAS;kEACV;;;;;;;;;;;;0DAMH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5B;GA9fa;;QACmB,2HAAA,CAAA,UAAO;;;KAD1B", "debugId": null}}, {"offset": {"line": 8557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/MiningUnitsTable.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { MiningRig } from '@/components/icons';\nimport { Calendar, TrendingUp, DollarSign } from 'lucide-react';\nimport { formatCurrency, formatTHS, formatDate, formatNumber } from '@/lib/utils';\n\ninterface MiningUnit {\n  id: string;\n  thsAmount: number;\n  investmentAmount: number;\n  startDate: string;\n  expiryDate: string;\n  dailyROI: number;\n  totalEarned: number;\n  status: 'ACTIVE' | 'EXPIRED';\n  createdAt: string;\n}\n\nexport const MiningUnitsTable: React.FC = () => {\n  const [miningUnits, setMiningUnits] = useState<MiningUnit[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchMiningUnits();\n  }, []);\n\n  const fetchMiningUnits = async () => {\n    try {\n      const response = await fetch('/api/mining-units', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setMiningUnits(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch mining units:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateProgress = (unit: MiningUnit) => {\n    const maxEarnings = unit.investmentAmount * 5;\n    const progress = (unit.totalEarned / maxEarnings) * 100;\n    return Math.min(progress, 100);\n  };\n\n  const getDaysRemaining = (expiryDate: string) => {\n    const expiry = new Date(expiryDate);\n    const now = new Date();\n    const diffTime = expiry.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(diffDays, 0);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return 'bg-eco-100 text-eco-700';\n      case 'EXPIRED':\n        return 'bg-gray-100 text-gray-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <MiningRig className=\"h-5 w-5 text-solar-500\" />\n            <span>Mining Units</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"animate-pulse space-y-4\">\n            {Array.from({ length: 3 }).map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded-lg\"></div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <MiningRig className=\"h-5 w-5 text-solar-500\" />\n          <span>Mining Units ({miningUnits.length})</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {miningUnits.length > 0 ? (\n          <div className=\"space-y-4\">\n            {/* Desktop Table */}\n            <div className=\"hidden md:block overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-200\">\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Mining Power</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Investment</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Daily ROI</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Total Earned</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Progress</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Expires</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Status</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {miningUnits.map((unit) => (\n                    <tr key={unit.id} className=\"border-b border-gray-100 hover:bg-gray-50\">\n                      <td className=\"py-4 px-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <MiningRig className=\"h-4 w-4 text-solar-500\" />\n                          <span className=\"font-medium\">{formatTHS(unit.thsAmount)}</span>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <span className=\"font-medium\">{formatCurrency(unit.investmentAmount)}</span>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <span className=\"text-eco-600 font-medium\">\n                          {formatNumber(unit.dailyROI, 2)}%\n                        </span>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <div>\n                          <span className=\"font-medium text-eco-600\">\n                            {formatCurrency(unit.totalEarned)}\n                          </span>\n                          <div className=\"text-xs text-gray-500\">\n                            / {formatCurrency(unit.investmentAmount * 5)} max\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-eco-500 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${calculateProgress(unit)}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {formatNumber(calculateProgress(unit), 1)}%\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <div>\n                          <span className=\"text-sm\">{formatDate(unit.expiryDate)}</span>\n                          <div className=\"text-xs text-gray-500\">\n                            {getDaysRemaining(unit.expiryDate)} days left\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>\n                          {unit.status}\n                        </span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"md:hidden space-y-4\">\n              {miningUnits.map((unit) => (\n                <div key={unit.id} className=\"bg-gray-50 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <MiningRig className=\"h-5 w-5 text-solar-500\" />\n                      <span className=\"font-semibold\">{formatTHS(unit.thsAmount)}</span>\n                    </div>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>\n                      {unit.status}\n                    </span>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4 mb-3\">\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Investment</div>\n                      <div className=\"font-medium\">{formatCurrency(unit.investmentAmount)}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Daily ROI</div>\n                      <div className=\"font-medium text-eco-600\">{formatNumber(unit.dailyROI, 2)}%</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Total Earned</div>\n                      <div className=\"font-medium text-eco-600\">{formatCurrency(unit.totalEarned)}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Days Left</div>\n                      <div className=\"font-medium\">{getDaysRemaining(unit.expiryDate)}</div>\n                    </div>\n                  </div>\n\n                  <div className=\"mb-2\">\n                    <div className=\"flex justify-between text-xs text-gray-500 mb-1\">\n                      <span>Progress to 5x</span>\n                      <span>{formatNumber(calculateProgress(unit), 1)}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-eco-500 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${calculateProgress(unit)}%` }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500\">\n                    Started: {formatDate(unit.startDate)} • Expires: {formatDate(unit.expiryDate)}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Summary */}\n            <div className=\"mt-6 p-4 bg-solar-50 rounded-lg\">\n              <h4 className=\"font-medium text-dark-900 mb-2\">Summary</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-600\">Total Mining Power: </span>\n                  <span className=\"font-medium\">\n                    {formatTHS(miningUnits.reduce((sum, unit) => sum + unit.thsAmount, 0))}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Total Investment: </span>\n                  <span className=\"font-medium\">\n                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0))}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Total Earned: </span>\n                  <span className=\"font-medium text-eco-600\">\n                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.totalEarned, 0))}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <MiningRig className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Mining Units</h3>\n            <p className=\"text-gray-500 mb-4\">\n              You haven't purchased any mining units yet. Start mining to earn daily returns!\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAEA;;;AANA;;;;;AAoBO,MAAM,mBAA6B;;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc,KAAK,gBAAgB,GAAG;QAC5C,MAAM,WAAW,AAAC,KAAK,WAAW,GAAG,cAAe;QACpD,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,IAAI,KAAK;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,OAAO,OAAO,KAAK,IAAI,OAAO;QAC/C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,2IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,2IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;;gCAAK;gCAAe,YAAY,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAG5C,6LAAC,mIAAA,CAAA,cAAW;0BACT,YAAY,MAAM,GAAG,kBACpB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;kDACC,cAAA,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;;;;;;;;;;;;kDAGlE,6LAAC;kDACE,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;gDAAiB,WAAU;;kEAC1B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,2IAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,6LAAC;oEAAK,WAAU;8EAAe,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;;gEACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,EAAE;gEAAG;;;;;;;;;;;;kEAGpC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW;;;;;;8EAElC,6LAAC;oEAAI,WAAU;;wEAAwB;wEAClC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB,GAAG;wEAAG;;;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,kBAAkB,MAAM,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGlD,6LAAC;gEAAI,WAAU;;oEACZ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,OAAO;oEAAG;;;;;;;;;;;;;kEAG9C,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAW,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;8EACrD,6LAAC;oEAAI,WAAU;;wEACZ,iBAAiB,KAAK,UAAU;wEAAE;;;;;;;;;;;;;;;;;;kEAIzC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;sEACzF,KAAK,MAAM;;;;;;;;;;;;+CA9CT,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;sCAwDxB,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;oCAAkB,WAAU;;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAiB,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8DAE3D,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;8DACzF,KAAK,MAAM;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;;8DAEpE,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;;gEAA4B,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,EAAE;gEAAG;;;;;;;;;;;;;8DAE5E,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEAA4B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW;;;;;;;;;;;;8DAE5E,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEAAe,iBAAiB,KAAK,UAAU;;;;;;;;;;;;;;;;;;sDAIlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,OAAO;gEAAG;;;;;;;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,kBAAkB,MAAM,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAKpD,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC3B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;gDAAE;gDAAa,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;;mCA5CtE,KAAK,EAAE;;;;;;;;;;sCAmDrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;;;;;;;;;;;;sDAGvE,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;;;;;;;sDAGnF,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAOpF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GApPa;KAAA", "debugId": null}}, {"offset": {"line": 9399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/SupportCenter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Card, CardHeader, CardT<PERSON>le, CardContent, Modal } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  MessageCircle, \n  Plus, \n  Clock, \n  CheckCircle, \n  AlertCircle,\n  Send,\n  Bell,\n  Settings,\n  Mail,\n  Smartphone\n} from 'lucide-react';\nimport { formatDateTime } from '@/lib/utils';\n\ninterface SupportTicket {\n  id: string;\n  subject: string;\n  message: string;\n  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';\n  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';\n  createdAt: string;\n  updatedAt: string;\n  responses: Array<{\n    id: string;\n    message: string;\n    isAdmin: boolean;\n    createdAt: string;\n  }>;\n}\n\ninterface NotificationSettings {\n  emailNotifications: boolean;\n  pushNotifications: boolean;\n  smsNotifications: boolean;\n  marketingEmails: boolean;\n}\n\nexport const SupportCenter: React.FC = () => {\n  const [tickets, setTickets] = useState<SupportTicket[]>([]);\n  const [notifications, setNotifications] = useState<NotificationSettings>({\n    emailNotifications: true,\n    pushNotifications: true,\n    smsNotifications: false,\n    marketingEmails: false,\n  });\n  const [loading, setLoading] = useState(true);\n  const [showNewTicketModal, setShowNewTicketModal] = useState(false);\n  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);\n  const [newTicketForm, setNewTicketForm] = useState({\n    subject: '',\n    message: '',\n    priority: 'MEDIUM' as const,\n  });\n  const [newResponse, setNewResponse] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n\n  useEffect(() => {\n    fetchTickets();\n    fetchNotificationSettings();\n  }, []);\n\n  const fetchTickets = async () => {\n    try {\n      const response = await fetch('/api/support/tickets', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setTickets(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch tickets:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchNotificationSettings = async () => {\n    try {\n      const response = await fetch('/api/user/notification-settings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setNotifications(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch notification settings:', error);\n    }\n  };\n\n  const createTicket = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setSubmitting(true);\n\n    try {\n      const response = await fetch('/api/support/tickets', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(newTicketForm),\n      });\n\n      if (response.ok) {\n        setNewTicketForm({ subject: '', message: '', priority: 'MEDIUM' });\n        setShowNewTicketModal(false);\n        fetchTickets();\n      }\n    } catch (error) {\n      console.error('Failed to create ticket:', error);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const addResponse = async (ticketId: string) => {\n    if (!newResponse.trim()) return;\n\n    try {\n      const response = await fetch(`/api/support/tickets/${ticketId}/responses`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ message: newResponse }),\n      });\n\n      if (response.ok) {\n        setNewResponse('');\n        fetchTickets();\n        // Update selected ticket\n        const updatedTickets = await fetch('/api/support/tickets', {\n          credentials: 'include',\n        });\n        if (updatedTickets.ok) {\n          const data = await updatedTickets.json();\n          const updatedTicket = data.data.find((t: SupportTicket) => t.id === ticketId);\n          if (updatedTicket) {\n            setSelectedTicket(updatedTicket);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Failed to add response:', error);\n    }\n  };\n\n  const updateNotificationSettings = async (settings: NotificationSettings) => {\n    try {\n      const response = await fetch('/api/user/notification-settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(settings),\n      });\n\n      if (response.ok) {\n        setNotifications(settings);\n      }\n    } catch (error) {\n      console.error('Failed to update notification settings:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'OPEN':\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      case 'IN_PROGRESS':\n        return <Clock className=\"h-4 w-4 text-solar-500\" />;\n      case 'RESOLVED':\n      case 'CLOSED':\n        return <CheckCircle className=\"h-4 w-4 text-eco-500\" />;\n      default:\n        return <MessageCircle className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'OPEN':\n        return 'bg-red-100 text-red-700';\n      case 'IN_PROGRESS':\n        return 'bg-solar-100 text-solar-700';\n      case 'RESOLVED':\n      case 'CLOSED':\n        return 'bg-eco-100 text-eco-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'URGENT':\n        return 'bg-red-100 text-red-700';\n      case 'HIGH':\n        return 'bg-orange-100 text-orange-700';\n      case 'MEDIUM':\n        return 'bg-solar-100 text-solar-700';\n      case 'LOW':\n        return 'bg-gray-100 text-gray-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Support Center</h2>\n          <p className=\"text-gray-600 mt-1\">Get help and manage your notifications</p>\n        </div>\n        <Button\n          onClick={() => setShowNewTicketModal(true)}\n          className=\"flex items-center gap-2\"\n        >\n          <Plus className=\"h-4 w-4\" />\n          New Ticket\n        </Button>\n      </div>\n\n      <Grid cols={{ default: 1, lg: 2 }} gap={8}>\n        {/* Support Tickets */}\n        <div className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <MessageCircle className=\"h-5 w-5\" />\n                Support Tickets\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              {tickets.length > 0 ? (\n                <div className=\"space-y-3\">\n                  {tickets.map((ticket) => (\n                    <div\n                      key={ticket.id}\n                      onClick={() => setSelectedTicket(ticket)}\n                      className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\"\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <h4 className=\"font-medium text-gray-900 truncate flex-1\">\n                          {ticket.subject}\n                        </h4>\n                        <div className=\"flex items-center gap-2 ml-2\">\n                          {getStatusIcon(ticket.status)}\n                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(ticket.status)}`}>\n                            {ticket.status}\n                          </span>\n                        </div>\n                      </div>\n                      <p className=\"text-sm text-gray-600 mb-2 line-clamp-2\">\n                        {ticket.message}\n                      </p>\n                      <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                        <span className={`px-2 py-1 rounded-full ${getPriorityColor(ticket.priority)}`}>\n                          {ticket.priority}\n                        </span>\n                        <span>{formatDateTime(ticket.createdAt)}</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <MessageCircle className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Support Tickets</h3>\n                  <p className=\"text-gray-600\">You haven't created any support tickets yet.</p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Notification Settings */}\n        <div className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Settings className=\"h-5 w-5\" />\n                Notification Settings\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Mail className=\"h-5 w-5 text-gray-500\" />\n                  <div>\n                    <p className=\"font-medium text-gray-900\">Email Notifications</p>\n                    <p className=\"text-sm text-gray-600\">Receive updates via email</p>\n                  </div>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications.emailNotifications}\n                    onChange={(e) => updateNotificationSettings({\n                      ...notifications,\n                      emailNotifications: e.target.checked,\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-solar-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-solar-500\"></div>\n                </label>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Bell className=\"h-5 w-5 text-gray-500\" />\n                  <div>\n                    <p className=\"font-medium text-gray-900\">Push Notifications</p>\n                    <p className=\"text-sm text-gray-600\">Browser notifications</p>\n                  </div>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications.pushNotifications}\n                    onChange={(e) => updateNotificationSettings({\n                      ...notifications,\n                      pushNotifications: e.target.checked,\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-solar-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-solar-500\"></div>\n                </label>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Smartphone className=\"h-5 w-5 text-gray-500\" />\n                  <div>\n                    <p className=\"font-medium text-gray-900\">SMS Notifications</p>\n                    <p className=\"text-sm text-gray-600\">Text message alerts</p>\n                  </div>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications.smsNotifications}\n                    onChange={(e) => updateNotificationSettings({\n                      ...notifications,\n                      smsNotifications: e.target.checked,\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-solar-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-solar-500\"></div>\n                </label>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </Grid>\n\n      {/* New Ticket Modal */}\n      <Modal\n        isOpen={showNewTicketModal}\n        onClose={() => setShowNewTicketModal(false)}\n        title=\"Create Support Ticket\"\n      >\n        <form onSubmit={createTicket} className=\"space-y-4\">\n          <Input\n            label=\"Subject\"\n            value={newTicketForm.subject}\n            onChange={(e) => setNewTicketForm(prev => ({ ...prev, subject: e.target.value }))}\n            placeholder=\"Brief description of your issue\"\n            required\n          />\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Priority\n            </label>\n            <select\n              value={newTicketForm.priority}\n              onChange={(e) => setNewTicketForm(prev => ({ ...prev, priority: e.target.value as any }))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n            >\n              <option value=\"LOW\">Low</option>\n              <option value=\"MEDIUM\">Medium</option>\n              <option value=\"HIGH\">High</option>\n              <option value=\"URGENT\">Urgent</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Message\n            </label>\n            <textarea\n              value={newTicketForm.message}\n              onChange={(e) => setNewTicketForm(prev => ({ ...prev, message: e.target.value }))}\n              placeholder=\"Describe your issue in detail...\"\n              rows={4}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n              required\n            />\n          </div>\n\n          <div className=\"flex space-x-3\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => setShowNewTicketModal(false)}\n              className=\"flex-1\"\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              loading={submitting}\n              className=\"flex-1\"\n            >\n              Create Ticket\n            </Button>\n          </div>\n        </form>\n      </Modal>\n\n      {/* Ticket Detail Modal */}\n      {selectedTicket && (\n        <Modal\n          isOpen={!!selectedTicket}\n          onClose={() => setSelectedTicket(null)}\n          title={`Ticket: ${selectedTicket.subject}`}\n          size=\"xl\"\n        >\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2\">\n              {getStatusIcon(selectedTicket.status)}\n              <span className={`text-sm px-2 py-1 rounded-full ${getStatusColor(selectedTicket.status)}`}>\n                {selectedTicket.status}\n              </span>\n              <span className={`text-sm px-2 py-1 rounded-full ${getPriorityColor(selectedTicket.priority)}`}>\n                {selectedTicket.priority}\n              </span>\n            </div>\n\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <p className=\"text-sm text-gray-600 mb-2\">Original Message:</p>\n              <p className=\"text-gray-900\">{selectedTicket.message}</p>\n              <p className=\"text-xs text-gray-500 mt-2\">\n                Created: {formatDateTime(selectedTicket.createdAt)}\n              </p>\n            </div>\n\n            {selectedTicket.responses.length > 0 && (\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-gray-900\">Responses:</h4>\n                {selectedTicket.responses.map((response) => (\n                  <div\n                    key={response.id}\n                    className={`p-3 rounded-lg ${\n                      response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'\n                    }`}\n                  >\n                    <p className=\"text-gray-900\">{response.message}</p>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {response.isAdmin ? 'Support Team' : 'You'} • {formatDateTime(response.createdAt)}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {selectedTicket.status !== 'CLOSED' && (\n              <div className=\"space-y-3\">\n                <textarea\n                  value={newResponse}\n                  onChange={(e) => setNewResponse(e.target.value)}\n                  placeholder=\"Add a response...\"\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n                />\n                <Button\n                  onClick={() => addResponse(selectedTicket.id)}\n                  disabled={!newResponse.trim()}\n                  className=\"w-full\"\n                >\n                  <Send className=\"h-4 w-4 mr-2\" />\n                  Send Response\n                </Button>\n              </div>\n            )}\n          </div>\n        </Modal>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAjBA;;;;;;AA0CO,MAAM,gBAA0B;;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,oBAAoB;QACpB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;YACA;QACF;kCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,WAAW,KAAK,IAAI;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,iBAAiB,KAAK,IAAI;gBAC5B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;oBAAE,SAAS;oBAAI,SAAS;oBAAI,UAAU;gBAAS;gBAChE,sBAAsB;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,CAAC,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAY;YAC9C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf;gBACA,yBAAyB;gBACzB,MAAM,iBAAiB,MAAM,MAAM,wBAAwB;oBACzD,aAAa;gBACf;gBACA,IAAI,eAAe,EAAE,EAAE;oBACrB,MAAM,OAAO,MAAM,eAAe,IAAI;oBACtC,MAAM,gBAAgB,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAqB,EAAE,EAAE,KAAK;oBACpE,IAAI,eAAe;wBACjB,kBAAkB;oBACpB;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,6BAA6B,OAAO;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,sBAAsB;wBACrC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAKhC,6LAAC,uIAAA,CAAA,OAAI;gBAAC,MAAM;oBAAE,SAAS;oBAAG,IAAI;gBAAE;gBAAG,KAAK;;kCAEtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIzC,6LAAC,mIAAA,CAAA,cAAW;8CACT,QAAQ,MAAM,GAAG,kBAChB,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gDAEC,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,OAAO,OAAO;;;;;;0EAEjB,6LAAC;gEAAI,WAAU;;oEACZ,cAAc,OAAO,MAAM;kFAC5B,6LAAC;wEAAK,WAAW,CAAC,+BAA+B,EAAE,eAAe,OAAO,MAAM,GAAG;kFAC/E,OAAO,MAAM;;;;;;;;;;;;;;;;;;kEAIpB,6LAAC;wDAAE,WAAU;kEACV,OAAO,OAAO;;;;;;kEAEjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,uBAAuB,EAAE,iBAAiB,OAAO,QAAQ,GAAG;0EAC3E,OAAO,QAAQ;;;;;;0EAElB,6LAAC;0EAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS;;;;;;;;;;;;;+CAtBnC,OAAO,EAAE;;;;;;;;;6DA4BpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,kBAAkB;4DACzC,UAAU,CAAC,IAAM,2BAA2B;oEAC1C,GAAG,aAAa;oEAChB,oBAAoB,EAAE,MAAM,CAAC,OAAO;gEACtC;4DACA,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAInB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,iBAAiB;4DACxC,UAAU,CAAC,IAAM,2BAA2B;oEAC1C,GAAG,aAAa;oEAChB,mBAAmB,EAAE,MAAM,CAAC,OAAO;gEACrC;4DACA,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAInB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,gBAAgB;4DACvC,UAAU,CAAC,IAAM,2BAA2B;oEAC1C,GAAG,aAAa;oEAChB,kBAAkB,EAAE,MAAM,CAAC,OAAO;gEACpC;4DACA,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,6LAAC,oIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,OAAM;0BAEN,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC,oIAAA,CAAA,QAAK;4BACJ,OAAM;4BACN,OAAO,cAAc,OAAO;4BAC5B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BAC/E,aAAY;4BACZ,QAAQ;;;;;;sCAGV,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,cAAc,QAAQ;oCAC7B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAQ,CAAC;oCACvF,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAI3B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,cAAc,OAAO;oCAC5B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC/E,aAAY;oCACZ,MAAM;oCACN,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YAQN,gCACC,6LAAC,oIAAA,CAAA,QAAK;gBACJ,QAAQ,CAAC,CAAC;gBACV,SAAS,IAAM,kBAAkB;gBACjC,OAAO,CAAC,QAAQ,EAAE,eAAe,OAAO,EAAE;gBAC1C,MAAK;0BAEL,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,eAAe,MAAM;8CACpC,6LAAC;oCAAK,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,MAAM,GAAG;8CACvF,eAAe,MAAM;;;;;;8CAExB,6LAAC;oCAAK,WAAW,CAAC,+BAA+B,EAAE,iBAAiB,eAAe,QAAQ,GAAG;8CAC3F,eAAe,QAAQ;;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAE,WAAU;8CAAiB,eAAe,OAAO;;;;;;8CACpD,6LAAC;oCAAE,WAAU;;wCAA6B;wCAC9B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;;;;;;;;;;;;;wBAIpD,eAAe,SAAS,CAAC,MAAM,GAAG,mBACjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4B;;;;;;gCACzC,eAAe,SAAS,CAAC,GAAG,CAAC,CAAC,yBAC7B,6LAAC;wCAEC,WAAW,CAAC,eAAe,EACzB,SAAS,OAAO,GAAG,0CAA0C,cAC7D;;0DAEF,6LAAC;gDAAE,WAAU;0DAAiB,SAAS,OAAO;;;;;;0DAC9C,6LAAC;gDAAE,WAAU;;oDACV,SAAS,OAAO,GAAG,iBAAiB;oDAAM;oDAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,SAAS;;;;;;;;uCAP7E,SAAS,EAAE;;;;;;;;;;;wBAcvB,eAAe,MAAM,KAAK,0BACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,YAAY,eAAe,EAAE;oCAC5C,UAAU,CAAC,YAAY,IAAI;oCAC3B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GA7da;KAAA", "debugId": null}}]}