import { prisma } from './prisma';
import { referralDb, binaryPointsDb, transactionDb, adminSettingsDb, systemLogDb, walletBalanceDb } from './database';

// Check if user has active mining units (for binary tree display)
export async function hasActiveMiningUnits(userId: string): Promise<boolean> {
  try {
    const activeMiningUnits = await prisma.miningUnit.count({
      where: {
        userId,
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
    });

    return activeMiningUnits > 0;
  } catch (error) {
    console.error('Error checking active mining units:', error);
    return false;
  }
}

// Calculate total downline count for a specific side
export async function calculateDownlineCount(userId: string, side: 'LEFT' | 'RIGHT'): Promise<number> {
  try {
    const downlineUsers = await getDownlineUsers(userId, side);
    return downlineUsers.length;
  } catch (error) {
    console.error('Downline count calculation error:', error);
    return 0;
  }
}

// Find the optimal placement position in the weaker leg
async function findOptimalPlacementPosition(referrerId: string): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {
  try {
    // Calculate total downline counts for both sides
    const leftDownlineCount = await calculateDownlineCount(referrerId, 'LEFT');
    const rightDownlineCount = await calculateDownlineCount(referrerId, 'RIGHT');

    // Determine weaker leg based on total downline count
    const weakerSide: 'LEFT' | 'RIGHT' = leftDownlineCount <= rightDownlineCount ? 'LEFT' : 'RIGHT';

    // Find the next available spot in the weaker leg
    const availableSpot = await findNextAvailableSpotInLeg(referrerId, weakerSide);

    if (availableSpot) {
      return availableSpot;
    }

    // Fallback: if no spot found in weaker leg, try the other side
    const strongerSide: 'LEFT' | 'RIGHT' = weakerSide === 'LEFT' ? 'RIGHT' : 'LEFT';
    const fallbackSpot = await findNextAvailableSpotInLeg(referrerId, strongerSide);

    if (fallbackSpot) {
      return fallbackSpot;
    }

    // Final fallback: place directly under referrer
    const existingReferrals = await referralDb.findByReferrerId(referrerId);
    const hasLeft = existingReferrals.some(r => r.placementSide === 'LEFT');
    const hasRight = existingReferrals.some(r => r.placementSide === 'RIGHT');

    if (!hasLeft) {
      return { userId: referrerId, side: 'LEFT' };
    } else if (!hasRight) {
      return { userId: referrerId, side: 'RIGHT' };
    }

    // If both sides are occupied, place in the weaker side
    return { userId: referrerId, side: weakerSide };

  } catch (error) {
    console.error('Optimal placement position error:', error);
    // Fallback to left side
    return { userId: referrerId, side: 'LEFT' };
  }
}

// Enhanced place new user in binary tree with weaker leg algorithm
export async function placeUserInBinaryTree(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {
  try {
    // Find optimal placement position using advanced weaker leg algorithm
    const optimalPosition = await findOptimalPlacementPosition(referrerId);

    // Create referral relationship with the optimal parent
    await referralDb.create({
      referrerId: optimalPosition.userId,
      referredId: newUserId,
      placementSide: optimalPosition.side,
    });

    // Update the parent's left/right referral IDs
    const updateData = optimalPosition.side === 'LEFT'
      ? { leftReferralId: newUserId }
      : { rightReferralId: newUserId };

    await prisma.user.update({
      where: { id: optimalPosition.userId },
      data: updateData,
    });

    // Create sponsor relationship (separate from binary placement)
    // The sponsor is always the original referrer, regardless of binary placement
    await createSponsorRelationship(referrerId, newUserId);

    // Update cached tree counts for affected users
    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);

    return optimalPosition.side;

  } catch (error) {
    console.error('Binary tree placement error:', error);
    throw error;
  }
}

// Create sponsor relationship (separate from binary placement)
async function createSponsorRelationship(sponsorId: string, newUserId: string): Promise<void> {
  try {
    // Update the new user's referrerId field to track sponsor
    await prisma.user.update({
      where: { id: newUserId },
      data: { referrerId: sponsorId },
    });

    // Update sponsor's direct referral count
    await prisma.user.update({
      where: { id: sponsorId },
      data: {
        directReferralCount: { increment: 1 },
        updatedAt: new Date(),
      },
    });

    // Mark referral as direct sponsor if the binary placement parent is the same as sponsor
    await prisma.referral.updateMany({
      where: {
        referrerId: sponsorId,
        referredId: newUserId,
      },
      data: {
        isDirectSponsor: true,
      },
    });

  } catch (error) {
    console.error('Sponsor relationship creation error:', error);
    // Don't throw error as this is supplementary to binary placement
  }
}

// Update cached downline counts for a user
export async function updateCachedDownlineCounts(userId: string): Promise<void> {
  try {
    const leftCount = await calculateDownlineCount(userId, 'LEFT');
    const rightCount = await calculateDownlineCount(userId, 'RIGHT');

    await prisma.user.update({
      where: { id: userId },
      data: {
        totalLeftDownline: leftCount,
        totalRightDownline: rightCount,
        lastTreeUpdate: new Date(),
      },
    });
  } catch (error) {
    console.error('Update cached downline counts error:', error);
  }
}

// Get cached downline counts (with fallback to real-time calculation)
export async function getCachedDownlineCounts(userId: string): Promise<{ left: number; right: number; total: number }> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        totalLeftDownline: true,
        totalRightDownline: true,
        lastTreeUpdate: true,
      },
    });

    if (!user) {
      return { left: 0, right: 0, total: 0 };
    }

    // Check if cache is recent (within last 30 minutes for more accurate counts)
    const cacheAge = user.lastTreeUpdate ? Date.now() - user.lastTreeUpdate.getTime() : Infinity;
    const cacheValidDuration = 30 * 60 * 1000; // 30 minutes

    if (cacheAge < cacheValidDuration && user.totalLeftDownline !== null && user.totalRightDownline !== null) {
      // Use cached values
      return {
        left: user.totalLeftDownline,
        right: user.totalRightDownline,
        total: user.totalLeftDownline + user.totalRightDownline,
      };
    } else {
      // Cache is stale or missing, recalculate and update
      const leftCount = await calculateDownlineCount(userId, 'LEFT');
      const rightCount = await calculateDownlineCount(userId, 'RIGHT');

      // Update cache asynchronously
      updateCachedDownlineCounts(userId).catch(console.error);

      return {
        left: leftCount,
        right: rightCount,
        total: leftCount + rightCount,
      };
    }
  } catch (error) {
    console.error('Get cached downline counts error:', error);
    return { left: 0, right: 0, total: 0 };
  }
}

// Find optimal placement in specific side with weaker leg logic (LEGACY - for backward compatibility)
async function findOptimalPlacementInSide(referrerId: string, targetSide: 'LEFT' | 'RIGHT'): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {
  try {
    // First, try to find the next available spot in the target side
    const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);

    if (availableSpot) {
      return availableSpot;
    }

    // If no spot available, find the position with smallest downline in that side
    const sideUsers = await getDownlineUsers(referrerId, targetSide);

    // Find the user with the smallest total downline in the target side
    let optimalUser = referrerId;
    let minDownlineCount = Infinity;

    for (const sideUser of sideUsers) {
      const leftCount = await calculateDownlineCount(sideUser.id, 'LEFT');
      const rightCount = await calculateDownlineCount(sideUser.id, 'RIGHT');
      const totalDownline = leftCount + rightCount;

      if (totalDownline < minDownlineCount) {
        // Check if this user has available spots
        const userReferrals = await referralDb.findByReferrerId(sideUser.id);
        const hasLeft = userReferrals.some(r => r.placementSide === 'LEFT');
        const hasRight = userReferrals.some(r => r.placementSide === 'RIGHT');

        if (!hasLeft || !hasRight) {
          minDownlineCount = totalDownline;
          optimalUser = sideUser.id;
        }
      }
    }

    // Determine which side to place in for the optimal user
    const optimalUserReferrals = await referralDb.findByReferrerId(optimalUser);
    const hasLeft = optimalUserReferrals.some(r => r.placementSide === 'LEFT');
    const hasRight = optimalUserReferrals.some(r => r.placementSide === 'RIGHT');

    if (!hasLeft) {
      return { userId: optimalUser, side: 'LEFT' };
    } else if (!hasRight) {
      return { userId: optimalUser, side: 'RIGHT' };
    }

    // If both sides occupied, use weaker leg logic
    const leftCount = await calculateDownlineCount(optimalUser, 'LEFT');
    const rightCount = await calculateDownlineCount(optimalUser, 'RIGHT');
    const weakerSide: 'LEFT' | 'RIGHT' = leftCount <= rightCount ? 'LEFT' : 'RIGHT';

    return { userId: optimalUser, side: weakerSide };

  } catch (error) {
    console.error('Optimal placement in side error:', error);
    return { userId: referrerId, side: targetSide };
  }
}

// NEW: Find deepest available position in LEFT side only (strict left-side placement)
async function findDeepestLeftPosition(referrerId: string): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {
  try {
    // Start from the referrer and traverse down the LEFT side only
    let currentUserId = referrerId;
    let currentLevel = 0;
    const maxDepth = 20; // Prevent infinite loops

    while (currentLevel < maxDepth) {
      // Verify current user exists
      const userExists = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { id: true },
      });

      if (!userExists) {
        // User doesn't exist, fallback to referrer
        return { userId: referrerId, side: 'LEFT' };
      }

      // Check if current user has a LEFT spot available
      const currentReferrals = await referralDb.findByReferrerId(currentUserId);
      const hasLeft = currentReferrals.some(r => r.placementSide === 'LEFT');

      if (!hasLeft) {
        // Found an available LEFT spot
        return { userId: currentUserId, side: 'LEFT' };
      }

      // Move to the LEFT child and continue traversing
      const leftChild = currentReferrals.find(r => r.placementSide === 'LEFT');
      if (!leftChild) {
        // This shouldn't happen if hasLeft is true, but safety check
        return { userId: currentUserId, side: 'LEFT' };
      }

      currentUserId = leftChild.referredId;
      currentLevel++;
    }

    // If we've reached max depth, place at the last position
    return { userId: currentUserId, side: 'LEFT' };

  } catch (error) {
    console.error('Find deepest left position error:', error);
    return { userId: referrerId, side: 'LEFT' };
  }
}

// NEW: Find deepest available position in RIGHT side only (strict right-side placement)
async function findDeepestRightPosition(referrerId: string): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {
  try {
    // Start from the referrer and traverse down the RIGHT side only
    let currentUserId = referrerId;
    let currentLevel = 0;
    const maxDepth = 20; // Prevent infinite loops

    while (currentLevel < maxDepth) {
      // Verify current user exists
      const userExists = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { id: true },
      });

      if (!userExists) {
        // User doesn't exist, fallback to referrer
        return { userId: referrerId, side: 'RIGHT' };
      }

      // Check if current user has a RIGHT spot available
      const currentReferrals = await referralDb.findByReferrerId(currentUserId);
      const hasRight = currentReferrals.some(r => r.placementSide === 'RIGHT');

      if (!hasRight) {
        // Found an available RIGHT spot
        return { userId: currentUserId, side: 'RIGHT' };
      }

      // Move to the RIGHT child and continue traversing
      const rightChild = currentReferrals.find(r => r.placementSide === 'RIGHT');
      if (!rightChild) {
        // This shouldn't happen if hasRight is true, but safety check
        return { userId: currentUserId, side: 'RIGHT' };
      }

      currentUserId = rightChild.referredId;
      currentLevel++;
    }

    // If we've reached max depth, place at the last position
    return { userId: currentUserId, side: 'RIGHT' };

  } catch (error) {
    console.error('Find deepest right position error:', error);
    return { userId: referrerId, side: 'RIGHT' };
  }
}

// Enhanced place user in specific side with weaker leg algorithm (LEGACY - for backward compatibility)
export async function placeUserInSpecificSide(referrerId: string, newUserId: string, side: 'LEFT' | 'RIGHT'): Promise<'LEFT' | 'RIGHT'> {
  try {
    // Find optimal placement position within the specified side
    const optimalPosition = await findOptimalPlacementInSide(referrerId, side);

    // Create referral relationship with the optimal parent
    await referralDb.create({
      referrerId: optimalPosition.userId,
      referredId: newUserId,
      placementSide: optimalPosition.side,
    });

    // Update the parent's referral ID
    const updateData = optimalPosition.side === 'LEFT'
      ? { leftReferralId: newUserId }
      : { rightReferralId: newUserId };

    await prisma.user.update({
      where: { id: optimalPosition.userId },
      data: updateData,
    });

    // Create sponsor relationship (separate from binary placement)
    await createSponsorRelationship(referrerId, newUserId);

    // Update cached tree counts for affected users
    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);

    return optimalPosition.side;

  } catch (error) {
    console.error('Specific side placement error:', error);
    throw error;
  }
}

// NEW: Place user strictly in LEFT side only (deepest available left position)
export async function placeUserInLeftSideOnly(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {
  try {
    // Find the deepest available position in the LEFT side
    const optimalPosition = await findDeepestLeftPosition(referrerId);

    // Create referral relationship with the optimal parent
    await referralDb.create({
      referrerId: optimalPosition.userId,
      referredId: newUserId,
      placementSide: optimalPosition.side,
    });

    // Update the parent's left referral ID
    await prisma.user.update({
      where: { id: optimalPosition.userId },
      data: { leftReferralId: newUserId },
    });

    // Create sponsor relationship (separate from binary placement)
    await createSponsorRelationship(referrerId, newUserId);

    // Update cached tree counts for affected users
    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);

    return optimalPosition.side;

  } catch (error) {
    console.error('Left side only placement error:', error);
    throw error;
  }
}

// NEW: Place user strictly in RIGHT side only (deepest available right position)
export async function placeUserInRightSideOnly(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {
  try {
    // Find the deepest available position in the RIGHT side
    const optimalPosition = await findDeepestRightPosition(referrerId);

    // Create referral relationship with the optimal parent
    await referralDb.create({
      referrerId: optimalPosition.userId,
      referredId: newUserId,
      placementSide: optimalPosition.side,
    });

    // Update the parent's right referral ID
    await prisma.user.update({
      where: { id: optimalPosition.userId },
      data: { rightReferralId: newUserId },
    });

    // Create sponsor relationship (separate from binary placement)
    await createSponsorRelationship(referrerId, newUserId);

    // Update cached tree counts for affected users
    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);

    return optimalPosition.side;

  } catch (error) {
    console.error('Right side only placement error:', error);
    throw error;
  }
}

// NEW: Main placement function that routes to appropriate algorithm based on referral link type
export async function placeUserByReferralType(
  referrerId: string,
  newUserId: string,
  referralType: 'general' | 'left' | 'right'
): Promise<'LEFT' | 'RIGHT'> {
  try {
    switch (referralType) {
      case 'left':
        // Strict left-side placement: find deepest available left position
        return await placeUserInLeftSideOnly(referrerId, newUserId);

      case 'right':
        // Strict right-side placement: find deepest available right position
        return await placeUserInRightSideOnly(referrerId, newUserId);

      case 'general':
      default:
        // Default weaker leg placement
        return await placeUserInBinaryTree(referrerId, newUserId);
    }
  } catch (error) {
    console.error('Placement by referral type error:', error);
    throw error;
  }
}

// Find next available spot in a specific leg
async function findNextAvailableSpotInLeg(rootUserId: string, targetSide: 'LEFT' | 'RIGHT'): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' } | null> {
  try {
    // Get the first user in the target leg
    const rootReferrals = await referralDb.findByReferrerId(rootUserId);
    const firstInLeg = rootReferrals.find(r => r.placementSide === targetSide);

    if (!firstInLeg) {
      // The target side is completely empty
      return { userId: rootUserId, side: targetSide };
    }

    // Traverse down the leg to find the first available spot
    const queue = [firstInLeg.referredId];

    while (queue.length > 0) {
      const currentUserId = queue.shift()!;
      const currentReferrals = await referralDb.findByReferrerId(currentUserId);

      // Check if this user has any empty spots
      const hasLeft = currentReferrals.some(r => r.placementSide === 'LEFT');
      const hasRight = currentReferrals.some(r => r.placementSide === 'RIGHT');

      if (!hasLeft) {
        return { userId: currentUserId, side: 'LEFT' };
      }
      if (!hasRight) {
        return { userId: currentUserId, side: 'RIGHT' };
      }

      // Add children to queue for further traversal
      currentReferrals.forEach(r => {
        queue.push(r.referredId);
      });
    }

    return null; // No available spot found
  } catch (error) {
    console.error('Find available spot error:', error);
    return null;
  }
}

// Process direct referral bonus (10% of investment) - Added directly to sponsor's wallet
// ONLY active sponsors (with active mining units) receive commissions
export async function processDirectReferralBonus(referrerId: string, investmentAmount: number) {
  try {
    // Check if sponsor is active (has active mining units)
    const isActive = await hasActiveMiningUnits(referrerId);

    if (!isActive) {
      console.log(`Skipping direct referral bonus for inactive sponsor ${referrerId} - no active mining units`);
      return 0; // Return 0 commission for inactive sponsors
    }

    const bonusPercentage = parseFloat(await adminSettingsDb.get('DIRECT_REFERRAL_BONUS') || '10');
    const bonusAmount = (investmentAmount * bonusPercentage) / 100;

    // Add commission directly to sponsor's wallet balance
    const sponsorWallet = await walletBalanceDb.getOrCreate(referrerId);
    await walletBalanceDb.updateBalance(referrerId, {
      availableBalance: sponsorWallet.availableBalance + bonusAmount,
    });

    // Create direct referral transaction
    await transactionDb.create({
      userId: referrerId,
      type: 'DIRECT_REFERRAL',
      amount: bonusAmount,
      description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount})`,
      status: 'COMPLETED',
    });

    // Update referral commission earned
    await prisma.referral.updateMany({
      where: {
        referrerId,
        referred: {
          miningUnits: {
            some: {
              investmentAmount,
            },
          },
        },
      },
      data: {
        commissionEarned: {
          increment: bonusAmount,
        },
      },
    });

    console.log(`Direct referral bonus of $${bonusAmount} awarded to active sponsor ${referrerId}`);
    return bonusAmount;

  } catch (error) {
    console.error('Direct referral bonus error:', error);
    throw error;
  }
}

// Add points to binary system when someone makes an investment ($100 = 1 point)
export async function addBinaryPoints(userId: string, investmentAmount: number) {
  try {
    // Calculate points: $100 investment = 1 point
    const points = Math.floor(investmentAmount / 100);

    if (points <= 0) return; // No points to add if investment is less than $100

    // Find all upline users and add points to their binary system (ONLY active upliners)
    const uplineUsers = await getUplineUsers(userId);

    for (const uplineUser of uplineUsers) {
      // Check if upline user is active (has active mining units)
      const isActive = await hasActiveMiningUnits(uplineUser.id);

      if (!isActive) {
        console.log(`Skipping inactive user ${uplineUser.id} - no active mining units`);
        continue; // Skip inactive users
      }

      // Determine which side this user is on relative to upline
      const placementSide = await getUserPlacementSide(uplineUser.id, userId);

      if (placementSide) {
        // Add points to the appropriate side
        const pointsToAdd = placementSide === 'LEFT'
          ? { leftPoints: points }
          : { rightPoints: points };

        await binaryPointsDb.upsert({
          userId: uplineUser.id,
          ...pointsToAdd,
        });

        console.log(`Added ${points} points to ${placementSide} side for active user ${uplineUser.id}`);
      }
    }

  } catch (error) {
    console.error('Binary points addition error:', error);
    throw error;
  }
}

// Get all upline users for a given user
async function getUplineUsers(userId: string): Promise<Array<{ id: string; email: string }>> {
  try {
    const uplineUsers = [];
    let currentUserId = userId;

    // Traverse up the tree (maximum 10 levels to prevent infinite loops)
    for (let level = 0; level < 10; level++) {
      const referral = await prisma.referral.findFirst({
        where: { referredId: currentUserId },
        include: {
          referrer: {
            select: { id: true, email: true },
          },
        },
      });

      if (!referral) break;

      uplineUsers.push(referral.referrer);
      currentUserId = referral.referrerId;
    }

    return uplineUsers;

  } catch (error) {
    console.error('Upline users fetch error:', error);
    return [];
  }
}

// Get all ACTIVE upline users for a given user (skip inactive users)
async function getActiveUplineUsers(userId: string): Promise<Array<{ id: string; email: string; isActive: boolean }>> {
  try {
    const uplineUsers = [];
    let currentUserId = userId;

    // Traverse up the tree (maximum 10 levels to prevent infinite loops)
    for (let level = 0; level < 10; level++) {
      const referral = await prisma.referral.findFirst({
        where: { referredId: currentUserId },
        include: {
          referrer: {
            select: { id: true, email: true, isActive: true },
          },
        },
      });

      if (!referral) break;

      // Only add active users to the list
      if (referral.referrer.isActive) {
        uplineUsers.push(referral.referrer);
      }

      // Continue traversing up regardless of active status
      currentUserId = referral.referrerId;
    }

    return uplineUsers;

  } catch (error) {
    console.error('Active upline users fetch error:', error);
    return [];
  }
}

// Determine which side a user is on relative to an upline user
async function getUserPlacementSide(uplineUserId: string, userId: string): Promise<'LEFT' | 'RIGHT' | null> {
  try {
    // Check direct placement first
    const directReferral = await prisma.referral.findFirst({
      where: {
        referrerId: uplineUserId,
        referredId: userId,
      },
    });
    
    if (directReferral) {
      return directReferral.placementSide;
    }
    
    // Check indirect placement by traversing down the tree
    const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');
    const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');
    
    if (leftSideUsers.some(u => u.id === userId)) {
      return 'LEFT';
    }
    
    if (rightSideUsers.some(u => u.id === userId)) {
      return 'RIGHT';
    }
    
    return null;
    
  } catch (error) {
    console.error('Placement side determination error:', error);
    return null;
  }
}

// Get all downline users for a specific side
async function getDownlineUsers(userId: string, side: 'LEFT' | 'RIGHT'): Promise<Array<{ id: string }>> {
  try {
    const downlineUsers = [];
    const visited = new Set<string>();

    // Start with the direct placement on the specified side
    const initialReferrals = await prisma.referral.findMany({
      where: {
        referrerId: userId,
        placementSide: side,
      },
      select: {
        referredId: true,
      },
    });

    // Use BFS to traverse the entire subtree
    const queue = initialReferrals.map(r => r.referredId);

    while (queue.length > 0) {
      const currentUserId = queue.shift()!;

      // Skip if already visited (prevent infinite loops)
      if (visited.has(currentUserId)) continue;
      visited.add(currentUserId);

      // Add current user to downline
      downlineUsers.push({ id: currentUserId });

      // Get all referrals (both LEFT and RIGHT) from current user
      const referrals = await prisma.referral.findMany({
        where: {
          referrerId: currentUserId,
        },
        select: {
          referredId: true,
        },
      });

      // Add all children to queue for further traversal
      for (const referral of referrals) {
        if (!visited.has(referral.referredId)) {
          queue.push(referral.referredId);
        }
      }
    }

    return downlineUsers;

  } catch (error) {
    console.error('Downline users fetch error:', error);
    return [];
  }
}

// Get all downline users (both sides combined) for total team count
async function getAllDownlineUsers(userId: string): Promise<Array<{ id: string; isActive: boolean }>> {
  try {
    const downlineUsers = [];
    const visited = new Set<string>();

    // Get all direct referrals (both LEFT and RIGHT)
    const initialReferrals = await prisma.referral.findMany({
      where: {
        referrerId: userId,
      },
      select: {
        referredId: true,
      },
    });

    // Use BFS to traverse the entire binary tree
    const queue = initialReferrals.map(r => r.referredId);

    while (queue.length > 0) {
      const currentUserId = queue.shift()!;

      // Skip if already visited (prevent infinite loops)
      if (visited.has(currentUserId)) continue;
      visited.add(currentUserId);

      // Get user info including active status
      const user = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { id: true, isActive: true },
      });

      if (user) {
        downlineUsers.push({ id: user.id, isActive: user.isActive });

        // Get all referrals from current user
        const referrals = await prisma.referral.findMany({
          where: {
            referrerId: currentUserId,
          },
          select: {
            referredId: true,
          },
        });

        // Add all children to queue for further traversal
        for (const referral of referrals) {
          if (!visited.has(referral.referredId)) {
            queue.push(referral.referredId);
          }
        }
      }
    }

    return downlineUsers;

  } catch (error) {
    console.error('All downline users fetch error:', error);
    return [];
  }
}

// Process weekly binary matching (15:00 UTC on Saturdays)
export async function processBinaryMatching() {
  try {
    console.log('Starting binary matching process...');

    const maxPointsPerSide = parseFloat(await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '2000');
    const pointValue = parseFloat(await adminSettingsDb.get('BINARY_POINT_VALUE') || '10'); // Dynamic point value from settings
    
    // Get all users with binary points
    const usersWithPoints = await prisma.binaryPoints.findMany({
      where: {
        OR: [
          { leftPoints: { gt: 0 } },
          { rightPoints: { gt: 0 } },
        ],
      },
      include: {
        user: {
          select: { id: true, email: true },
        },
      },
    });
    
    console.log(`Processing binary matching for ${usersWithPoints.length} users`);

    const matchingResults = [];

    for (const userPoints of usersWithPoints) {
      try {
        // Calculate matching points (minimum of left and right, capped at max per side)
        const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);
        const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);
        const matchedPoints = Math.min(leftPoints, rightPoints);

        if (matchedPoints > 0) {
          // Calculate direct payout: 1 point = $10
          const userPayout = matchedPoints * pointValue;

          try {
            // Create binary bonus transaction
            await transactionDb.create({
              userId: userPoints.userId,
              type: 'BINARY_BONUS',
              amount: userPayout,
              description: `Binary matching bonus - ${matchedPoints} points matched at $${pointValue} per point`,
              status: 'COMPLETED',
            });

            // Add to wallet balance
            await walletBalanceDb.addEarnings(userPoints.userId, userPayout);

            // Calculate remaining points after matching - reset weaker side to 0
            // Example: User has 7 left, 5 right -> 5 matched, left becomes 2, right becomes 0
            const remainingLeftPoints = Math.max(0, userPoints.leftPoints - matchedPoints);
            const remainingRightPoints = Math.max(0, userPoints.rightPoints - matchedPoints);

            // Reset the weaker side to 0 after matching (proper binary matching rule)
            const finalLeftPoints = userPoints.leftPoints > userPoints.rightPoints ? remainingLeftPoints : 0;
            const finalRightPoints = userPoints.rightPoints > userPoints.leftPoints ? remainingRightPoints : 0;

            // Update binary points - reset weaker side to 0, keep stronger side remainder
            await prisma.binaryPoints.update({
              where: { id: userPoints.id },
              data: {
                leftPoints: finalLeftPoints, // Reset weaker side to 0
                rightPoints: finalRightPoints, // Reset weaker side to 0
                matchedPoints: { increment: matchedPoints },
                totalMatched: { increment: matchedPoints }, // Track lifetime total
                lastMatchDate: new Date(), // Track when points were last matched
                flushDate: new Date(), // Track when points were processed
              },
            });

            matchingResults.push({
              userId: userPoints.userId,
              matchedPoints,
              payout: userPayout,
              remainingLeftPoints: finalLeftPoints,
              remainingRightPoints: finalRightPoints,
            });

            console.log(`User ${userPoints.userId}: ${matchedPoints} points matched, $${userPayout.toFixed(2)} payout, remaining: L${finalLeftPoints} R${finalRightPoints}`);
          } catch (payoutError) {
            console.error(`Error processing payout for user ${userPoints.userId}:`, payoutError);
            // Continue with next user instead of failing the entire process
          }
        } else {
          // No matching possible, but still reset excess points if over the limit
          const excessLeft = Math.max(0, userPoints.leftPoints - maxPointsPerSide);
          const excessRight = Math.max(0, userPoints.rightPoints - maxPointsPerSide);

          if (excessLeft > 0 || excessRight > 0) {
            try {
              // Reset excess points (pressure out)
              await prisma.binaryPoints.update({
                where: { id: userPoints.id },
                data: {
                  leftPoints: Math.min(userPoints.leftPoints, maxPointsPerSide),
                  rightPoints: Math.min(userPoints.rightPoints, maxPointsPerSide),
                  flushDate: new Date(),
                },
              });

              console.log(`User ${userPoints.userId}: Excess points reset - L${excessLeft} R${excessRight} points flushed`);
            } catch (flushError) {
              console.error(`Error flushing excess points for user ${userPoints.userId}:`, flushError);
            }
          }
        }
        
      } catch (userError) {
        console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);
      }
    }
    
    // Log the binary matching process
    await systemLogDb.create({
      action: 'BINARY_MATCHING_PROCESSED',
      details: {
        usersProcessed: usersWithPoints.length,
        totalMatchedPoints: matchingResults.reduce((sum, r) => sum + r.matchedPoints, 0),
        pointValue,
        totalPayouts: matchingResults.reduce((sum, r) => sum + r.payout, 0),
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Binary matching completed. Processed ${matchingResults.length} users with total payouts: $${matchingResults.reduce((sum, r) => sum + r.payout, 0).toFixed(2)}`);

    return {
      success: true,
      usersProcessed: matchingResults.length,
      totalPayouts: matchingResults.reduce((sum, r) => sum + r.payout, 0),
      matchingResults,
    };
    
  } catch (error) {
    console.error('Binary matching process error:', error);
    throw error;
  }
}

// Get sponsor information for a user
export async function getSponsorInfo(userId: string): Promise<{ id: string; email: string; firstName: string; lastName: string } | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { referrerId: true },
    });

    if (!user?.referrerId) return null;

    const sponsor = await prisma.user.findUnique({
      where: { id: user.referrerId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      },
    });

    return sponsor;
  } catch (error) {
    console.error('Sponsor info fetch error:', error);
    return null;
  }
}

// Get direct referral count for a user (sponsored users)
export async function getDirectReferralCount(userId: string): Promise<number> {
  try {
    const count = await prisma.user.count({
      where: { referrerId: userId },
    });
    return count;
  } catch (error) {
    console.error('Direct referral count error:', error);
    return 0;
  }
}

// Get total team count (all downline users in binary tree) - uses cached values
export async function getTotalTeamCount(userId: string): Promise<{ left: number; right: number; total: number }> {
  try {
    return await getCachedDownlineCounts(userId);
  } catch (error) {
    console.error('Total team count error:', error);
    return { left: 0, right: 0, total: 0 };
  }
}

// Get detailed team statistics
export async function getDetailedTeamStats(userId: string): Promise<{
  directReferrals: number;
  leftTeam: number;
  rightTeam: number;
  totalTeam: number;
  activeMembers: number;
  recentJoins: number; // Last 30 days
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { directReferralCount: true },
    });

    const teamCounts = await getCachedDownlineCounts(userId);

    // Get all downline users for accurate active member count
    const allDownlineUsers = await getAllDownlineUsers(userId);
    const activeMembers = allDownlineUsers.filter(u => u.isActive).length;

    // Get recent joins (last 30 days) - direct referrals only
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentJoins = await prisma.user.count({
      where: {
        referrerId: userId,
        createdAt: { gte: thirtyDaysAgo },
      },
    });

    return {
      directReferrals: user?.directReferralCount || 0,
      leftTeam: teamCounts.left,
      rightTeam: teamCounts.right,
      totalTeam: teamCounts.total,
      activeMembers,
      recentJoins,
    };
  } catch (error) {
    console.error('Detailed team stats error:', error);
    return {
      directReferrals: 0,
      leftTeam: 0,
      rightTeam: 0,
      totalTeam: 0,
      activeMembers: 0,
      recentJoins: 0,
    };
  }
}

// Find all users in a specific generation (level) of the tree
export async function getUsersByGeneration(userId: string, generation: number): Promise<Array<{
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: Date;
  placementSide: 'LEFT' | 'RIGHT';
}>> {
  try {
    if (generation <= 0) return [];

    let currentLevelUsers = [{ id: userId, side: null as 'LEFT' | 'RIGHT' | null }];

    for (let level = 1; level <= generation; level++) {
      const nextLevelUsers = [];

      for (const currentUser of currentLevelUsers) {
        const referrals = await prisma.referral.findMany({
          where: { referrerId: currentUser.id },
          include: {
            referred: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                createdAt: true,
              },
            },
          },
        });

        for (const referral of referrals) {
          nextLevelUsers.push({
            id: referral.referredId,
            side: referral.placementSide,
          });
        }
      }

      currentLevelUsers = nextLevelUsers;
    }

    // Get full user details for the final generation
    const userDetails = await Promise.all(
      currentLevelUsers.map(async (user) => {
        const userInfo = await prisma.user.findUnique({
          where: { id: user.id },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            createdAt: true,
          },
        });

        return {
          ...userInfo!,
          placementSide: user.side!,
        };
      })
    );

    return userDetails.filter(Boolean);
  } catch (error) {
    console.error('Users by generation error:', error);
    return [];
  }
}

// Enhanced binary tree structure with detailed member information
export async function getBinaryTreeStructure(userId: string, depth = 3, expandedNodes: Set<string> = new Set()) {
  try {
    const buildTree = async (currentUserId: string, currentDepth: number, path: string = ''): Promise<any> => {
      if (currentDepth <= 0) return null;

      const user = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          createdAt: true,
        },
      });

      if (!user) return null;

      // Check if user has active mining units (for binary tree display)
      const isActive = await hasActiveMiningUnits(currentUserId);

      // Get sponsor information
      const sponsorInfo = await getSponsorInfo(currentUserId);

      // Get direct referral count
      const directReferralCount = await getDirectReferralCount(currentUserId);

      // Get team counts
      const teamCounts = await getTotalTeamCount(currentUserId);

      // Get direct referrals (binary placement)
      const leftReferral = await prisma.referral.findFirst({
        where: {
          referrerId: currentUserId,
          placementSide: 'LEFT',
        },
        include: {
          referred: {
            select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },
          },
        },
      });

      const rightReferral = await prisma.referral.findFirst({
        where: {
          referrerId: currentUserId,
          placementSide: 'RIGHT',
        },
        include: {
          referred: {
            select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },
          },
        },
      });

      // Get binary points
      const binaryPoints = await binaryPointsDb.findByUserId(currentUserId);

      // Determine if we should load children for infinite depth support
      // Load children if we have remaining depth AND either:
      // 1. We're within the initial depth (first 3 levels) - always show first 3 levels
      // 2. OR this node is explicitly expanded - show children of expanded nodes
      const isWithinInitialDepth = path.length < 3; // First 3 levels (root = 0, level 1 = 1 char, level 2 = 2 chars)
      const isNodeExpanded = expandedNodes.has(currentUserId);

      const shouldLoadChildren = currentDepth > 1 && (isWithinInitialDepth || isNodeExpanded);

      // Check if children exist (for showing expand button)
      const hasLeftChild = leftReferral !== null;
      const hasRightChild = rightReferral !== null;

      return {
        user: { ...user, isActive }, // Add computed isActive status
        sponsorInfo,
        directReferralCount,
        teamCounts,
        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },
        hasLeftChild,
        hasRightChild,
        leftChild: shouldLoadChildren && leftReferral ?
          await buildTree(leftReferral.referredId, currentDepth - 1, path + 'L') : null,
        rightChild: shouldLoadChildren && rightReferral ?
          await buildTree(rightReferral.referredId, currentDepth - 1, path + 'R') : null,
      };
    };

    return await buildTree(userId, depth);

  } catch (error) {
    console.error('Binary tree structure error:', error);
    throw error;
  }
}

// Load children for a specific node (for dynamic expansion)
export async function loadNodeChildren(userId: string): Promise<{
  leftChild: any | null;
  rightChild: any | null;
}> {
  try {
    // Get direct referrals (binary placement)
    const leftReferral = await prisma.referral.findFirst({
      where: {
        referrerId: userId,
        placementSide: 'LEFT',
      },
      include: {
        referred: {
          select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },
        },
      },
    });

    const rightReferral = await prisma.referral.findFirst({
      where: {
        referrerId: userId,
        placementSide: 'RIGHT',
      },
      include: {
        referred: {
          select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },
        },
      },
    });

    const buildChildNode = async (referral: any) => {
      if (!referral) return null;

      const childUserId = referral.referredId;

      // Check if user has active mining units (for binary tree display)
      const isActive = await hasActiveMiningUnits(childUserId);

      // Get sponsor information
      const sponsorInfo = await getSponsorInfo(childUserId);

      // Get direct referral count
      const directReferralCount = await getDirectReferralCount(childUserId);

      // Get team counts
      const teamCounts = await getTotalTeamCount(childUserId);

      // Get binary points
      const binaryPoints = await binaryPointsDb.findByUserId(childUserId);

      // Check if this child has its own children
      const hasLeftChild = await prisma.referral.findFirst({
        where: { referrerId: childUserId, placementSide: 'LEFT' },
        select: { id: true }
      }) !== null;

      const hasRightChild = await prisma.referral.findFirst({
        where: { referrerId: childUserId, placementSide: 'RIGHT' },
        select: { id: true }
      }) !== null;

      return {
        user: { ...referral.referred, isActive }, // Add computed isActive status
        sponsorInfo,
        directReferralCount,
        teamCounts,
        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },
        hasLeftChild,
        hasRightChild,
        leftChild: null, // Will be loaded on demand
        rightChild: null, // Will be loaded on demand
      };
    };

    const leftChild = await buildChildNode(leftReferral);
    const rightChild = await buildChildNode(rightReferral);

    return { leftChild, rightChild };

  } catch (error) {
    console.error('Load node children error:', error);
    return { leftChild: null, rightChild: null };
  }
}

// Search for users in the binary tree
export async function searchUsersInTree(rootUserId: string, searchTerm: string, maxResults = 20): Promise<Array<{
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: Date;
  placementPath: string; // e.g., "L-R-L" showing path from root
  generation: number;
  sponsorInfo?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}>> {
  try {
    const searchPattern = `%${searchTerm.toLowerCase()}%`;

    // Get all downline users that match the search term
    const leftUsers = await getDownlineUsers(rootUserId, 'LEFT');
    const rightUsers = await getDownlineUsers(rootUserId, 'RIGHT');
    const allDownlineIds = [...leftUsers, ...rightUsers].map(u => u.id);

    if (allDownlineIds.length === 0) return [];

    const matchingUsers = await prisma.user.findMany({
      where: {
        id: { in: allDownlineIds },
        OR: [
          { email: { contains: searchTerm, mode: 'insensitive' } },
          { firstName: { contains: searchTerm, mode: 'insensitive' } },
          { lastName: { contains: searchTerm, mode: 'insensitive' } },
        ],
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        referrerId: true,
      },
      take: maxResults,
    });

    // Get placement path and sponsor info for each user
    const results = await Promise.all(
      matchingUsers.map(async (user) => {
        const placementPath = await getPlacementPath(rootUserId, user.id);
        const generation = placementPath.split('-').length;

        let sponsorInfo = undefined;
        if (user.referrerId) {
          sponsorInfo = await prisma.user.findUnique({
            where: { id: user.referrerId },
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          });
        }

        return {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          createdAt: user.createdAt,
          placementPath,
          generation,
          sponsorInfo: sponsorInfo || undefined,
        };
      })
    );

    return results;
  } catch (error) {
    console.error('Search users in tree error:', error);
    return [];
  }
}

// Get placement path from root to a specific user (e.g., "L-R-L")
async function getPlacementPath(rootUserId: string, targetUserId: string): Promise<string> {
  try {
    if (rootUserId === targetUserId) return 'ROOT';

    const path: string[] = [];
    let currentUserId = targetUserId;

    // Traverse up the tree to find path
    while (currentUserId !== rootUserId) {
      const referral = await prisma.referral.findFirst({
        where: { referredId: currentUserId },
      });

      if (!referral) break;

      path.unshift(referral.placementSide === 'LEFT' ? 'L' : 'R');
      currentUserId = referral.referrerId;

      // Prevent infinite loops
      if (path.length > 20) break;
    }

    return path.join('-') || 'UNKNOWN';
  } catch (error) {
    console.error('Get placement path error:', error);
    return 'UNKNOWN';
  }
}

// Update tree counts after a new user placement
async function updateTreeCountsAfterPlacement(sponsorId: string, placementParentId: string): Promise<void> {
  try {
    // Update counts for the sponsor (if different from placement parent)
    if (sponsorId !== placementParentId) {
      await updateCachedDownlineCounts(sponsorId);
    }

    // Update counts for the placement parent
    await updateCachedDownlineCounts(placementParentId);

    // Update counts for all upline users from the placement parent
    const uplineUsers = await getUplineUsers(placementParentId);
    const updatePromises = uplineUsers.map(user => updateCachedDownlineCounts(user.id));
    await Promise.all(updatePromises);

  } catch (error) {
    console.error('Update tree counts after placement error:', error);
    // Don't throw error as this is supplementary to placement
  }
}

// Bulk update tree counts for multiple users (for maintenance)
export async function bulkUpdateTreeCounts(userIds: string[]): Promise<void> {
  try {
    const updatePromises = userIds.map(userId => updateCachedDownlineCounts(userId));
    await Promise.all(updatePromises);
  } catch (error) {
    console.error('Bulk update tree counts error:', error);
  }
}

// Get tree health statistics
export async function getTreeHealthStats(rootUserId: string): Promise<{
  totalUsers: number;
  balanceRatio: number; // Ratio of smaller side to larger side (0-1, closer to 1 is more balanced)
  averageDepth: number;
  maxDepth: number;
  emptyPositions: number; // Available spots in the tree
}> {
  try {
    const teamCounts = await getCachedDownlineCounts(rootUserId);
    const totalUsers = teamCounts.total;

    // Calculate balance ratio
    const smallerSide = Math.min(teamCounts.left, teamCounts.right);
    const largerSide = Math.max(teamCounts.left, teamCounts.right);
    const balanceRatio = largerSide > 0 ? smallerSide / largerSide : 1;

    // Calculate tree depth statistics
    let maxDepth = 0;
    let totalDepth = 0;
    let userCount = 0;

    // BFS to calculate depths
    const queue = [{ userId: rootUserId, depth: 0 }];
    const visited = new Set<string>();

    while (queue.length > 0) {
      const { userId, depth } = queue.shift()!;

      if (visited.has(userId)) continue;
      visited.add(userId);

      maxDepth = Math.max(maxDepth, depth);
      totalDepth += depth;
      userCount++;

      const referrals = await prisma.referral.findMany({
        where: { referrerId: userId },
        select: { referredId: true },
      });

      for (const referral of referrals) {
        if (!visited.has(referral.referredId)) {
          queue.push({ userId: referral.referredId, depth: depth + 1 });
        }
      }
    }

    const averageDepth = userCount > 0 ? totalDepth / userCount : 0;

    // Calculate empty positions (theoretical max - actual users)
    const theoreticalMax = Math.pow(2, maxDepth + 1) - 1;
    const emptyPositions = Math.max(0, theoreticalMax - totalUsers);

    return {
      totalUsers,
      balanceRatio,
      averageDepth,
      maxDepth,
      emptyPositions,
    };
  } catch (error) {
    console.error('Tree health stats error:', error);
    return {
      totalUsers: 0,
      balanceRatio: 1,
      averageDepth: 0,
      maxDepth: 0,
      emptyPositions: 0,
    };
  }
}
