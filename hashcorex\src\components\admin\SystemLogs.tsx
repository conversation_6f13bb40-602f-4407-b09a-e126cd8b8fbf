'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';
import { 
  FileText, 
  Search, 
  Filter, 
  Download,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface SystemLog {
  id: string;
  action: string;
  userId?: string;
  user?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export const SystemLogs: React.FC = () => {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'all'>('today');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchLogs();
  }, [currentPage, searchTerm, filterAction, dateRange]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '50',
        search: searchTerm,
        action: filterAction,
        dateRange,
      });

      const response = await fetch(`/api/admin/logs?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setLogs(data.logs);
          setTotalPages(data.totalPages);
        }
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportLogs = async () => {
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        action: filterAction,
        dateRange,
        export: 'true',
      });

      const response = await fetch(`/api/admin/logs/export?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export logs:', error);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'USER_LOGIN':
      case 'USER_LOGOUT':
        return <User className="h-4 w-4 text-blue-400" />;
      case 'USER_REGISTER':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'MINING_PURCHASE':
      case 'WITHDRAWAL_REQUEST':
        return <Activity className="h-4 w-4 text-purple-400" />;
      case 'KYC_SUBMIT':
      case 'KYC_APPROVE':
      case 'KYC_REJECT':
        return <FileText className="h-4 w-4 text-orange-400" />;
      case 'ADMIN_ACTION':
        return <AlertTriangle className="h-4 w-4 text-red-400" />;
      default:
        return <Info className="h-4 w-4 text-slate-400" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'USER_LOGIN':
      case 'USER_REGISTER':
      case 'KYC_APPROVE':
        return 'text-green-300 bg-green-900/20 border border-green-700';
      case 'USER_LOGOUT':
        return 'text-blue-300 bg-blue-900/20 border border-blue-700';
      case 'MINING_PURCHASE':
      case 'WITHDRAWAL_REQUEST':
        return 'text-purple-300 bg-purple-900/20 border border-purple-700';
      case 'KYC_SUBMIT':
        return 'text-orange-300 bg-orange-900/20 border border-orange-700';
      case 'KYC_REJECT':
      case 'ADMIN_ACTION':
        return 'text-red-300 bg-red-900/20 border border-red-700';
      default:
        return 'text-slate-300 bg-slate-700 border border-slate-600';
    }
  };

  const parseLogDetails = (details: any) => {
    if (!details) return null;

    try {
      // If it's already an object, return it
      if (typeof details === 'object') return details;

      // If it's a string, try to parse it as JSON
      if (typeof details === 'string') {
        return JSON.parse(details);
      }

      return details;
    } catch {
      // If parsing fails, return the original string
      return details;
    }
  };

  const formatLogDetails = (details: any) => {
    const parsed = parseLogDetails(details);

    if (!parsed) return 'No details available';

    if (typeof parsed === 'string') return parsed;

    if (typeof parsed === 'object') {
      // Handle specific log types with better formatting
      if (parsed.type === 'CREDIT' || parsed.type === 'DEBIT') {
        return (
          <div className="space-y-1">
            <div><span className="text-slate-400">Type:</span> <span className="text-white">{parsed.type}</span></div>
            <div><span className="text-slate-400">Amount:</span> <span className="text-green-400">${parsed.amount}</span></div>
            <div><span className="text-slate-400">Reason:</span> <span className="text-white">{parsed.reason}</span></div>
            {parsed.description && <div><span className="text-slate-400">Description:</span> <span className="text-white">{parsed.description}</span></div>}
            {parsed.targetUser && <div><span className="text-slate-400">Target User:</span> <span className="text-white">{parsed.targetUser}</span></div>}
          </div>
        );
      }

      if (parsed.targetUserId || parsed.targetUserEmail) {
        return (
          <div className="space-y-1">
            {parsed.targetUserEmail && <div><span className="text-slate-400">User:</span> <span className="text-white">{parsed.targetUserEmail}</span></div>}
            {parsed.targetUserId && <div><span className="text-slate-400">User ID:</span> <span className="text-slate-300">{parsed.targetUserId}</span></div>}
            {parsed.amount && <div><span className="text-slate-400">Amount:</span> <span className="text-green-400">${parsed.amount}</span></div>}
            {parsed.reason && <div><span className="text-slate-400">Reason:</span> <span className="text-white">{parsed.reason}</span></div>}
          </div>
        );
      }

      // Generic object formatting
      return (
        <div className="space-y-1">
          {Object.entries(parsed).map(([key, value]) => (
            <div key={key}>
              <span className="text-slate-400 capitalize">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>{' '}
              <span className="text-white">{String(value)}</span>
            </div>
          ))}
        </div>
      );
    }

    return String(parsed);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">System Logs</h1>
          <p className="text-slate-400 mt-1">Monitor platform activity and user actions</p>
        </div>
        <Button
          onClick={exportLogs}
          variant="outline"
          className="flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
        >
          <Download className="h-4 w-4" />
          Export Logs
        </Button>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <select
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Actions</option>
                <option value="USER_LOGIN">User Login</option>
                <option value="USER_REGISTER">User Register</option>
                <option value="MINING_PURCHASE">Mining Purchase</option>
                <option value="WITHDRAWAL_REQUEST">Withdrawal Request</option>
                <option value="KYC_SUBMIT">KYC Submit</option>
                <option value="ADMIN_ACTION">Admin Action</option>
              </select>
            </div>
            <div>
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value as any)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="all">All Time</option>
              </select>
            </div>
            <div className="text-sm text-slate-400 flex items-center">
              <Activity className="h-4 w-4 mr-1" />
              {logs.length} logs found
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Logs Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <FileText className="h-5 w-5 text-blue-400" />
            Activity Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {logs.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No Logs Found</h3>
                <p className="text-slate-400">No activity logs match your current filters.</p>
              </div>
            ) : (
              logs.map((log) => (
                <div
                  key={log.id}
                  className="bg-slate-700 rounded-lg border border-slate-600 hover:border-slate-500 transition-all duration-200 hover:shadow-lg"
                >
                  {/* Header Section */}
                  <div className="flex items-center justify-between p-4 border-b border-slate-600">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-slate-600">
                        {getActionIcon(log.action)}
                      </div>
                      <div>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
                          {log.action.replace(/_/g, ' ')}
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-slate-400">
                      {formatDate(log.createdAt)}
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-4 space-y-3">
                    {log.user && (
                      <div className="flex items-center gap-2 p-2 bg-slate-800 rounded-lg">
                        <User className="h-4 w-4 text-blue-400" />
                        <div>
                          <span className="font-medium text-white">
                            {log.user.firstName} {log.user.lastName}
                          </span>
                          <span className="text-slate-400 ml-2 text-sm">({log.user.email})</span>
                        </div>
                      </div>
                    )}

                    {log.details && (
                      <div className="p-3 bg-slate-800 rounded-lg">
                        <div className="text-sm font-medium text-slate-300 mb-2">Details:</div>
                        <div className="text-sm">
                          {formatLogDetails(log.details)}
                        </div>
                      </div>
                    )}

                    {(log.ipAddress || log.userAgent) && (
                      <div className="p-2 bg-slate-800 rounded-lg">
                        <div className="text-xs text-slate-400 space-y-1">
                          {log.ipAddress && (
                            <div className="flex items-center gap-2">
                              <span className="font-medium">IP Address:</span>
                              <span className="text-slate-300">{log.ipAddress}</span>
                            </div>
                          )}
                          {log.userAgent && (
                            <div className="flex items-start gap-2">
                              <span className="font-medium whitespace-nowrap">User Agent:</span>
                              <span className="text-slate-300 break-all">{log.userAgent}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t border-slate-600">
              <div className="text-sm text-slate-400">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50"
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50"
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
